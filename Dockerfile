# Dockerfile
FROM php:8.2-fpm

# Установка зависимостей для расширений PHP и MySQL клиент (включая mysqldump)
RUN apt-get update && apt-get install -y \
    libzip-dev \
    zip \
    unzip \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libwebp-dev \
    libxml2-dev \
    libicu-dev \
    libonig-dev \
    libcurl4-openssl-dev \
    default-mysql-client \
    && rm -rf /var/lib/apt/lists/*

# Настройка расширения GD
RUN docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp

# Установка расширений PHP
RUN docker-php-ext-install \
    pdo \
    pdo_mysql \
    mysqli \
    zip \
    gd \
    exif \
    bcmath \
    intl \
    opcache \
    calendar \
    mbstring \
    xml \
    curl \
    soap

# Установка XDebug
RUN pecl install xdebug && docker-php-ext-enable xdebug

COPY ./xdebug.ini "${PHP_INI_DIR}/conf.d"

# Оптимизация PHP для production
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Скачивание и установка последней версии WordPress
WORKDIR /var/www/html
RUN curl -o wordpress.tar.gz https://wordpress.org/latest.tar.gz \
    && tar -xzf wordpress.tar.gz -C /var/www/html --strip-components=1 \
    && rm wordpress.tar.gz

# Очистка кеша
RUN apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*