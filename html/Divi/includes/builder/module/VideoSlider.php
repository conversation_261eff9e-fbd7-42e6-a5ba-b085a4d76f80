<?php

class ET_Builder_Module_Video_Slider extends ET_Builder_Module {
	/**
	 * Indicates if module has box shadow.
	 *
	 * @var bool
	 */
	public $has_box_shadow;

	function init() {
		$this->name                   = esc_html__( 'Video Slider', 'et_builder' );
		$this->plural                 = esc_html__( 'Video Sliders', 'et_builder' );
		$this->slug                   = 'et_pb_video_slider';
		$this->vb_support             = 'on';
		$this->child_slug             = 'et_pb_video_slider_item';
		$this->child_item_text        = esc_html__( 'Video', 'et_builder' );
		$this->main_css_element       = '.et_pb_video_slider%%order_class%%';
		$this->has_box_shadow         = false;
		$this->settings_modal_toggles = array(
			'general'  => array(
				'toggles' => array(
					'elements' => et_builder_i18n( 'Elements' ),
					'overlay'  => et_builder_i18n( 'Overlay' ),
				),
			),
			'advanced' => array(
				'toggles' => array(
					'colors' => esc_html__( 'Controls', 'et_builder' ),
				),
			),
		);

		$this->custom_css_fields = array(
			'play_button'    => array(
				'label'    => esc_html__( 'Play Button', 'et_builder' ),
				'selector' => '.et_pb_video_play',
			),
			'thumbnail_item' => array(
				'label'    => esc_html__( 'Thumbnail Item', 'et_builder' ),
				'selector' => '.et_pb_carousel_item',
			),
			'arrows'         => array(
				'label'    => esc_html__( 'Slider Arrows', 'et_builder' ),
				'selector' => '.et-pb-slider-arrows a',
			),
		);

		$this->advanced_fields = array(
			'borders'         => array(
				'default' => array(
					'css' => array(
						'main' => array(
							'border_radii'  => "{$this->main_css_element} .et_pb_slider, {$this->main_css_element} .et_pb_carousel_item",
							'border_styles' => "{$this->main_css_element} .et_pb_slider, {$this->main_css_element} .et_pb_carousel_item",
						),
					),
				),
			),
			'margin_padding'  => array(
				'css' => array(
					'important' => array( 'custom_margin' ), // needed to overwrite last module margin-bottom styling
				),
			),
			'max_width'       => array(
				'css' => array(
					'module_alignment' => '%%order_class%%.et_pb_video_slider.et_pb_module',
				),
			),
			'fonts'           => false,
			'text'            => false,
			'button'          => false,
			'box_shadow'      => array(
				'default' => array(
					'css' => array(
						'main'    => '%%order_class%%>.et_pb_slider, %%order_class%%>.et_pb_carousel .et_pb_carousel_item',
						'overlay' => 'inset',
					),
				),
			),
			'link_options'    => false,
			'position_fields' => array(
				'default' => 'relative',
			),
		);

		$this->help_videos = array(
			array(
				'id'   => 'gwTruYDcxoE',
				'name' => esc_html__( 'An introduction to the Video Slider module', 'et_builder' ),
			),
		);
	}

	function get_fields() {
		$fields = array(
			'show_image_overlay'      => array(
				'label'            => esc_html__( 'Show Image Overlays on Main Video', 'et_builder' ),
				'type'             => 'yes_no_button',
				'option_category'  => 'configuration',
				'options'          => array(
					'on'  => et_builder_i18n( 'Yes' ),
					'off' => et_builder_i18n( 'No' ),
				),
				'default_on_front' => 'off',
				'toggle_slug'      => 'overlay',
				'description'      => esc_html__( 'This option will cover the player UI on the main video. This image can either be uploaded in each video setting or auto-generated by Divi.', 'et_builder' ),
				'mobile_options'   => true,
				'hover'            => 'tabs',
			),
			'show_arrows'             => array(
				'label'            => esc_html__( 'Show Arrows', 'et_builder' ),
				'type'             => 'yes_no_button',
				'option_category'  => 'configuration',
				'options'          => array(
					'on'  => et_builder_i18n( 'Yes' ),
					'off' => et_builder_i18n( 'No' ),
				),
				'default_on_front' => 'on',
				'toggle_slug'      => 'elements',
				'description'      => esc_html__( 'This setting will turn on and off the navigation arrows.', 'et_builder' ),
				'mobile_options'   => true,
				'hover'            => 'tabs',
			),
			'show_thumbnails'         => array(
				'label'            => esc_html__( 'Slider Controls', 'et_builder' ),
				'type'             => 'select',
				'option_category'  => 'configuration',
				'options'          => array(
					'on'  => esc_html__( 'Use Thumbnail Track', 'et_builder' ),
					'off' => esc_html__( 'Use Dot Navigation', 'et_builder' ),
				),
				'default_on_front' => 'on',
				'toggle_slug'      => 'elements',
				'description'      => esc_html__( 'This setting will let you choose to use the thumbnail track controls below the slider or dot navigation at the bottom of the slider.', 'et_builder' ),
				'mobile_options'   => true,
				'hover'            => 'tabs',
			),
			'play_icon_color'         => array(
				'label'          => esc_html__( 'Play Icon Color', 'et_builder' ),
				'description'    => esc_html__( 'Here you can define a custom color for the play icon.', 'et_builder' ),
				'type'           => 'color-alpha',
				'custom_color'   => true,
				'tab_slug'       => 'advanced',
				'toggle_slug'    => 'colors',
				'hover'          => 'tabs',
				'mobile_options' => true,
				'sticky'         => true,
			),
			'font_icon'      => array(
				'label'          => esc_html__( 'Icon', 'et_builder' ),
				'toggle_slug'    => 'colors',
				'type'           => 'select_icon',
				'class'          => array( 'et-pb-font-icon' ),
				'description'    => esc_html__( 'Choose an icon to display with your blurb.', 'et_builder' ),
				'mobile_options' => true,
				'hover'          => 'tabs',
				'sticky'         => true,
				'tab_slug'       => 'advanced',
			),
			'use_icon_font_size'      => array(
				'label'            => esc_html__( 'Use Play Icon Font Size', 'et_builder' ),
				'description'      => esc_html__( 'If you would like to control the size of the icon, you must first enable this option.', 'et_builder' ),
				'type'             => 'yes_no_button',
				'options'          => array(
					'off' => et_builder_i18n( 'No' ),
					'on'  => et_builder_i18n( 'Yes' ),
				),
				'default_on_front' => 'off',
				'affects'          => array(
					'icon_font_size',
				),
				'tab_slug'         => 'advanced',
				'toggle_slug'      => 'colors',
				'option_category'  => 'font_option',
			),
			'icon_font_size'          => array(
				'label'            => esc_html__( 'Play Icon Font Size', 'et_builder' ),
				'description'      => esc_html__( 'Control the size of the icon by increasing or decreasing the font size.', 'et_builder' ),
				'type'             => 'range',
				'option_category'  => 'font_option',
				'tab_slug'         => 'advanced',
				'toggle_slug'      => 'colors',
				'allowed_units'    => array( '%', 'em', 'rem', 'px', 'cm', 'mm', 'in', 'pt', 'pc', 'ex', 'vh', 'vw' ),
				'default'          => '96px',
				'default_unit'     => 'px',
				'default_on_front' => '',
				'range_settings'   => array(
					'min'  => '1',
					'max'  => '120',
					'step' => '1',
				),
				'mobile_options'   => true,
				'depends_show_if'  => 'on',
				'responsive'       => true,
				'sticky'           => true,
				'hover'            => 'tabs',
			),
			'thumbnail_overlay_color' => array(
				'label'          => esc_html__( 'Thumbnail Overlay Color', 'et_builder' ),
				'description'    => esc_html__( 'Pick a color to use for the overlay that appears behind the play icon when hovering over the video.', 'et_builder' ),
				'type'           => 'color-alpha',
				'custom_color'   => true,
				'tab_slug'       => 'advanced',
				'toggle_slug'    => 'colors',
				'mobile_options' => true,
				'sticky'         => true,
			),
			'controls_color'          => array(
				'label'           => esc_html__( 'Slider Controls Color', 'et_builder' ),
				'type'            => 'select',
				'option_category' => 'color_option',
				'options'         => array(
					'light' => et_builder_i18n( 'Light' ),
					'dark'  => et_builder_i18n( 'Dark' ),
				),
				'tab_slug'        => 'advanced',
				'toggle_slug'     => 'colors',
				'description'     => esc_html__( 'This setting will make your slider controls either light or dark in color. Slider controls are either the arrows on the thumbnail track or the circles in dot navigation.', 'et_builder' ),
			),
		);
		return $fields;
	}

	public function get_transition_fields_css_props() {
		$fields = parent::get_transition_fields_css_props();

		$fields['play_icon_color'] = array( 'color' => '%%order_class%% .et_pb_video_play, %%order_class%% .et_pb_carousel .et_pb_video_play' );
		$fields['icon_font_size']  = array(
			'font-size'   => '%%order_class%% .et_pb_video_play, %%order_class%% .et_pb_carousel .et_pb_video_play',
			'margin-left' => '%%order_class%% .et_pb_video_play, %%order_class%% .et_pb_carousel .et_pb_video_play',
			'margin-top'  => '%%order_class%% .et_pb_video_play, %%order_class%% .et_pb_carousel .et_pb_video_play',
			'line-height' => '%%order_class%% .et_pb_video_play, %%order_class%% .et_pb_carousel .et_pb_video_play',
		);

		return $fields;
	}

	function before_render() {
		global $et_pb_slider_image_overlay,
			$et_pb_video_slider_sticky;

		$show_image_overlay = et_pb_multi_view_options( $this )->get_values( 'show_image_overlay' );

		$et_pb_slider_image_overlay = $show_image_overlay;

		$et_pb_video_slider_sticky = et_pb_sticky_options()->is_sticky_module( $this->props );
	}

	/**
	 * Renders the module output.
	 *
	 * @param  array  $attrs       List of attributes.
	 * @param  string $content     Content being processed.
	 * @param  string $render_slug Slug of module that is used for rendering output.
	 *
	 * @return string
	 */
	public function render( $attrs, $content, $render_slug ) {
		$multi_view         = et_pb_multi_view_options( $this );
		$show_arrows        = $this->props['show_arrows'];
		$show_thumbnails    = $this->props['show_thumbnails'];
		$controls_color     = $this->props['controls_color'];
		$use_icon_font_size = $this->props['use_icon_font_size'];

		global $et_pb_slider_image_overlay;

		$video_background          = $this->video_background();
		$parallax_image_background = $this->get_parallax_image_background();

		// Play Icon color.
		$this->generate_styles(
			array(
				'base_attr_name'                  => 'play_icon_color',
				'selector'                        => '%%order_class%% .et_pb_video_play, %%order_class%% .et_pb_carousel .et_pb_video_play',
				'hover_pseudo_selector_location'  => 'suffix',
				'sticky_pseudo_selector_location' => 'prefix',
				'css_property'                    => 'color',
				'important'                       => true,
				'render_slug'                     => $render_slug,
				'type'                            => 'color',
			)
		);

		// Play Icon Styles.
		$this->generate_styles(
			array(
				'utility_arg'    => 'icon_font_family_and_content',
				'render_slug'    => $render_slug,
				'base_attr_name' => 'font_icon',
				'important'      => true,
				'selector'       => '%%order_class%% .et_pb_video_play:before, %%order_class%% .et_pb_carousel .et_pb_video_play:before',
				'processor'      => array(
					'ET_Builder_Module_Helper_Style_Processor',
					'process_extended_icon',
				),
			)
		);

		// Icon Size.
		if ( 'off' !== $use_icon_font_size ) {
			// Icon Font Size.
			$this->generate_styles(
				array(
					'base_attr_name'                  => 'icon_font_size',
					'selector'                        => '%%order_class%% .et_pb_video_wrap .et_pb_video_play, %%order_class%% .et_pb_video_wrap .et_pb_carousel .et_pb_video_play',
					'hover_pseudo_selector_location'  => 'suffix',
					'sticky_pseudo_selector_location' => 'prefix',
					'render_slug'                     => $render_slug,
					'type'                            => 'range',

					// processed attr value can't be directly assigned to single css property so
					// custom processor is needed to render this attr.
					'processor'                       => array(
						'ET_Builder_Module_Helper_Style_Processor',
						'process_overlay_icon_font_size',
					),
				)
			);
		}

		// Thumbnail Overlay Color.
		$this->generate_styles(
			array(
				'hover'                           => false,
				'base_attr_name'                  => 'thumbnail_overlay_color',
				'selector'                        => '%%order_class%% .et_pb_carousel_item .et_pb_video_overlay_hover:hover, %%order_class%%.et_pb_video_slider .et_pb_slider:hover .et_pb_video_overlay_hover, %%order_class%% .et_pb_carousel_item.et-pb-active-control .et_pb_video_overlay_hover',
				'sticky_pseudo_selector_location' => 'prefix',
				'css_property'                    => 'background-color',
				'render_slug'                     => $render_slug,
				'type'                            => 'color',
			)
		);

		$slider_classname  = '';
		$slider_classname .= 'off' === $show_arrows ? ' et_pb_slider_no_arrows' : '';
		$slider_classname .= 'on' === $show_thumbnails ? ' et_pb_slider_carousel et_pb_slider_no_pagination' : '';
		$slider_classname .= 'off' === $show_thumbnails ? ' et_pb_slider_dots' : '';
		$slider_classname .= " et_pb_controls_{$controls_color}";

		$content = $this->content;

		// Module classnames
		if ( $this->has_box_shadow ) {
			$this->add_classname( 'et_pb_has_box_shadow' );
		}

		$multi_view_data_attr = $multi_view->render_attrs(
			array(
				'classes' => array(
					'et_pb_slider_no_arrows'     => array(
						'show_arrows' => 'off',
					),
					'et_pb_slider_carousel'      => array(
						'show_thumbnails' => 'on',
					),
					'et_pb_slider_no_pagination' => array(
						'show_thumbnails' => 'on',
					),
					'et_pb_slider_dots'          => array(
						'show_thumbnails' => 'off',
					),
				),
			)
		);

		$output = sprintf(
			'<div%3$s class="%4$s">
				%6$s
				%5$s
				%8$s
				%9$s
				<div class="et_pb_slider et_pb_preload%1$s"%7$s>
					<div class="et_pb_slides">
						%2$s
					</div>
				</div>
			</div>
			',
			esc_attr( $slider_classname ),
			$content,
			$this->module_id(),
			$this->module_classname( $render_slug ),
			$video_background,
			$parallax_image_background,
			$multi_view_data_attr,
			et_core_esc_previously( $this->background_pattern() ), // #8
			et_core_esc_previously( $this->background_mask() ) // #9
		);

		return $output;
	}
}

if ( et_builder_should_load_all_module_data() ) {
	new ET_Builder_Module_Video_Slider();
}
