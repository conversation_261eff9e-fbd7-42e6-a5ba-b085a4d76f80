<?php
/**
 * Background Mask Style - Square Stripes.
 *
 * @package Divi
 * @sub-package Builder
 * @since 4.15.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Direct access forbidden.' );
}

/**
 * Class ET_Builder_Mask_Square_Stripes
 *
 * @since 4.15.0
 */
class ET_Builder_Mask_Square_Stripes extends ET_Builder_Background_Mask_Style_Base {
	/**
	 * Configuration.
	 *
	 * @return array
	 */
	public function settings() {
		return array(
			'label'      => esc_html__( 'Square Stripes', 'et-builder' ),
			'svgContent' => array(
				'default'          => array(
					'landscape' => '<g>
										<polygon points="1479.38 478.41 1252.46 0 1097.51 0 1412.89 664.9 1920 424.36 1920 269.41 1479.38 478.41"/>
										<polygon points="1436.38 61.07 1545.87 291.92 1776.72 182.42 1690.19 0 1565.13 0 1436.38 61.07"/>
										<polygon points="1920 0 1845.14 0 1920 157.81 1920 0"/>
										<polygon points="1346.39 851.39 942.56 0 787.61 0 1279.9 1037.88 1920 734.26 1920 579.31 1346.39 851.39"/>
										<polygon points="1213.4 1224.37 632.66 0 0 0 0 1440 1920 1440 1920 889.22 1213.4 1224.37"/>
									</g>',
					'portrait'  => '<g>
										<rect x="1478.8" y="690" width="255.5" height="255.5" transform="translate(-195.44 767.4) rotate(-25.38)"/>
										<polygon points="1479.38 1174.41 1249.89 690.58 1733.72 461.08 1920 853.82 1920 527.14 1800.21 274.6 1063.4 624.08 1412.89 1360.9 1920 1120.36 1920 965.41 1479.38 1174.41"/>
										<polygon points="1213.4 1920.37 503.93 424.6 1399.1 0 0 0 0 2560 1920 2560 1920 1585.22 1213.4 1920.37"/>
										<polygon points="1346.39 1547.39 876.91 557.59 1866.71 88.1 1920 200.46 1920 0 1725.78 0 690.42 491.1 1279.9 1733.88 1920 1430.27 1920 1275.31 1346.39 1547.39"/>
									</g>',
					'square'    => '<g>
										<rect x="1478.8" y="210" width="255.5" height="255.5" transform="translate(10.26 721.09) rotate(-25.38)"/>
										<polygon points="1920 373.81 1920 47.14 1897.64 0 1742.69 0 1920 373.81"/>
										<polygon points="1479.38 694.41 1249.89 210.58 1693.84 0 1367.16 0 1063.4 144.08 1412.89 880.9 1920 640.36 1920 485.41 1479.38 694.41"/>
										<polygon points="1346.39 1067.39 876.91 77.59 1040.49 0 713.81 0 690.42 11.1 1279.9 1253.88 1920 950.26 1920 795.31 1346.39 1067.39"/>
										<polygon points="1213.4 1440.37 530.2 0 0 0 0 1920 1920 1920 1920 1105.22 1213.4 1440.37"/>
									</g>',
				),
				'default-inverted' => array(
					'landscape' => '<g>
										<polygon points="1920 889.22 1920 734.26 1279.9 1037.88 787.61 0 632.66 0 1213.4 1224.37 1920 889.22"/>
										<polygon points="1920 579.31 1920 424.36 1412.89 664.9 1097.51 0 942.56 0 1346.39 851.39 1920 579.31"/>
										<polygon points="1920 269.41 1920 157.81 1845.14 0 1690.19 0 1776.72 182.42 1545.87 291.92 1436.38 61.07 1565.13 0 1252.46 0 1479.38 478.41 1920 269.41"/>
									</g>',
					'portrait'  => '<g>
										<polygon points="1213.4 1920.37 1920 1585.22 1920 1430.27 1279.9 1733.88 690.42 491.1 1725.78 0 1399.1 0 503.93 424.6 1213.4 1920.37"/>
										<polygon points="876.91 557.59 1346.39 1547.39 1920 1275.31 1920 1120.36 1412.89 1360.9 1063.4 624.08 1800.21 274.6 1920 527.14 1920 200.46 1866.71 88.1 876.91 557.59"/>
										<path d="M1249.89,690.58l229.49,483.83,440.62-209V853.81L1733.72,461.08Zm296,297.34L1436.38,757.07l230.85-109.49,109.49,230.84Z"/>
									</g>',
					'square'    => '<g>
										<polygon points="1920 1105.22 1920 950.26 1279.9 1253.88 690.42 11.1 713.81 0 530.2 0 1213.4 1440.37 1920 1105.22"/>
										<polygon points="1346.39 1067.39 1920 795.31 1920 640.36 1412.89 880.9 1063.4 144.08 1367.16 0 1040.49 0 876.91 77.59 1346.39 1067.39"/>
										<polygon points="1920 0 1897.64 0 1920 47.14 1920 0"/>
										<path d="M1693.84,0,1249.89,210.58l229.49,483.83,440.62-209V373.81L1742.69,0Zm-148,507.92L1436.38,277.07l230.85-109.49,109.49,230.84Z"/>
									</g>',
				),
				'rotated'          => array(
					'landscape' => '<path d="M167.58,252.77,398.42,143.28l109.5,230.85L277.07,483.62ZM373.81,0H47.14L0,22.36v155Zm320.6,440.62L210.58,670.11,0,226.16V552.84L144.08,856.6,880.9,507.11,640.36,0H485.41Zm373,133L77.59,1043.09,0,879.51v326.68l11.1,23.39L1253.88,640.1,950.27,0h-155Zm373,133L0,1389.8V1440H1920V0H1105.22Z"/>',
					'portrait'  => '<path d="M167.58,252.77,398.42,143.28l109.5,230.85L277.07,483.62ZM373.81,0H47.14L0,22.36v155Zm320.6,440.62L210.58,670.11,0,226.16V552.84L144.08,856.6,880.9,507.11,640.36,0H485.41Zm373,133L77.59,1043.09,0,879.51v326.68l11.1,23.39L1253.88,640.1,950.27,0h-155Zm373,133L0,1389.8V2560H1920V0H1105.22Z"/>',
					'square'    => '<path d="M167.58,252.77,398.42,143.28l109.5,230.85L277.07,483.62ZM373.81,0H47.14L0,22.36v155Zm320.6,440.62L210.58,670.11,0,226.16V552.84L144.08,856.6,880.9,507.11,640.36,0H485.41Zm373,133L77.59,1043.09,0,879.51v326.68l11.1,23.39L1253.88,640.1,950.27,0h-155Zm373,133L0,1389.8V1920H1920V0H1105.22Z"/>',
				),
				'rotated-inverted' => array(
					'landscape' => '<path d="M77.59,1043.09,0,879.51V552.84L144.08,856.6,880.9,507.11,640.36,0H795.31l272.08,573.61Zm133-373L0,226.16V177.31L373.81,0h111.6l209,440.62Zm-43-417.34L277.07,483.62,507.92,374.13,398.42,143.28ZM1105.22,0h-155l303.61,640.1L11.1,1229.58,0,1206.19V1389.8L1440.37,706.6ZM0,0V22.36L47.14,0Z"/>',
					'portrait'  => '<path d="M694.41,440.62,485.41,0H373.81L0,177.31v48.85l210.58,444Zm-296-297.34,109.5,230.85L277.07,483.62,167.58,252.77ZM77.59,1043.09,0,879.51V552.84L144.08,856.6,880.9,507.11,640.36,0H795.31l272.08,573.61ZM1440.37,706.6,0,1389.8V1206.19l11.1,23.39L1253.88,640.1,950.27,0h155ZM47.14,0,0,22.36V0Z"/>',
					'square'    => '<path d="M77.59,1043.09,0,879.51V552.84L144.08,856.6,880.9,507.11,640.36,0H795.31l272.08,573.61Zm133-373L0,226.16V177.31L373.81,0h111.6l209,440.62Zm-43-417.34L277.07,483.62,507.92,374.13,398.42,143.28ZM0,0V22.36L47.14,0ZM1105.22,0h-155l303.61,640.1L11.1,1229.58,0,1206.19V1389.8L1440.37,706.6Z"/>',
				),
			),
		);
	}
}

return new ET_Builder_Mask_Square_Stripes();
