[{"search_terms": "arrow up direction previous collapse", "unicode": "&#x21;", "name": "Arrow Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow down direction expand next", "unicode": "&#x22;", "name": "Arrow Down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow left direction prevous back", "unicode": "&#x23;", "name": "Arrow Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow right direction next forward", "unicode": "&#x24;", "name": "Arrow Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow up left direction previous back", "unicode": "&#x25;", "name": "Arrow Up Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow up right direction next forward", "unicode": "&#x26;", "name": "Arrow Up Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow down right direction next forward", "unicode": "&#x27;", "name": "Arrow Down Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow down left direction previous back", "unicode": "&#x28;", "name": "Arrow Down Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow up down direction expand resize drag", "unicode": "&#x29;", "name": "Arrow Up Down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrows up down arrow up down direction expand resize drag", "unicode": "&#x2a;", "name": "Arrows Up Down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrows left right arrow right left direction expand resize drag", "unicode": "&#x2b;", "name": "Arrows Left Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow left right arrow right left direction expand resize drag", "unicode": "&#x2c;", "name": "Arrow Left Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow resize arrow expand resize drag", "unicode": "&#x2d;", "name": "Arrow Resize", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow resize arrow expand resize drag", "unicode": "&#x2e;", "name": "Arrow Resize", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrows condense arrow condense resize collapse close", "unicode": "&#x2f;", "name": "Arrows Condense", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrows expand arrow expand resize open", "unicode": "&#x30;", "name": "Arrows Expand", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "move arrow drag", "unicode": "&#x31;", "name": "Move", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret up arrow caret up direction previous collapse", "unicode": "&#x32;", "name": "Caret Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret down arrow caret down direction expand next", "unicode": "&#x33;", "name": "Caret down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret left arrow caret left direction prevous back", "unicode": "&#x34;", "name": "Caret Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret right arrow caret right direction next forward", "unicode": "&#x35;", "name": "Caret Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret up arrow caret up direction previous collapse", "unicode": "&#x36;", "name": "Double Caret Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret down arrow caret down direction expand next", "unicode": "&#x37;", "name": "Double Caret down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret left arrow caret left direction prevous back", "unicode": "&#x38;", "name": "Double Caret Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret right arrow caret right direction next forward", "unicode": "&#x39;", "name": "Double Caret Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret up arrow caret up direction previous collapse", "unicode": "&#x3a;", "name": "Caret Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret down arrow caret down direction expand next", "unicode": "&#x3b;", "name": "Caret down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret left arrow caret left direction prevous back", "unicode": "&#x3c;", "name": "Caret Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret right arrow caret right direction next forward", "unicode": "&#x3d;", "name": "Caret Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret up arrow caret up direction previous collapse", "unicode": "&#x3e;", "name": "Double Caret Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret down arrow caret down direction expand next", "unicode": "&#x3f;", "name": "Double Caret down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret left arrow caret left direction prevous back", "unicode": "&#x40;", "name": "Double Caret Left", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret right arrow caret right direction next forward", "unicode": "&#x41;", "name": "Double Caret Right", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret up arrow caret triangle up direction previous collapse", "unicode": "&#x42;", "name": "Caret Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret down arrow caret triangle down direction expand next", "unicode": "&#x43;", "name": "Caret down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret left arrow caret triangle left direction prevous back", "unicode": "&#x44;", "name": "Caret Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret right arrow caret triangle right direction next forward", "unicode": "&#x45;", "name": "Caret Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret up arrow caret triangle up direction previous collapse", "unicode": "&#x46;", "name": "Caret Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret down arrow caret triangle down direction expand next", "unicode": "&#x47;", "name": "Caret down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret left arrow caret triangle left direction prevous back", "unicode": "&#x48;", "name": "Caret Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret right arrow caret triangle right direction next forward", "unicode": "&#x49;", "name": "Caret Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow back arrow left direction prevous back", "unicode": "&#x4a;", "name": "Arrow Back", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "minus dash hyphen", "unicode": "&#x4b;", "name": "Minus", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "plus add cross", "unicode": "&#x4c;", "name": "Plus", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "close x cancel delete remove", "unicode": "&#x4d;", "name": "Close", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "check confirm", "unicode": "&#x4e;", "name": "Check", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "minus dash hyphen", "unicode": "&#x4f;", "name": "Minus", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "plus add cross", "unicode": "&#x50;", "name": "Plus", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "close x cancel delete remove", "unicode": "&#x51;", "name": "Close", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "check confirm", "unicode": "&#x52;", "name": "Check", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "zoom out zoom magnifying glass", "unicode": "&#x53;", "name": "Zoom Out", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "zoom in zoom magnifying glass", "unicode": "&#x54;", "name": "Zoom In", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "magnifying glass search zoom", "unicode": "&#x55;", "name": "Magnifying Glass", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "checkbox empty checkbox box square", "unicode": "&#x56;", "name": "Checkbox Empty", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "checkbox filled checkbox box square bullet", "unicode": "&#x57;", "name": "Checkbox Filled", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "box minus minus close collapse remove", "unicode": "&#x58;", "name": "Box Minus", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "box plus plus add cross expand", "unicode": "&#x59;", "name": "Box Plus", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "checkbox checked checkbox check selected", "unicode": "&#x5a;", "name": "Checkbox Checked", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "radio empty circle bullet radio", "unicode": "&#x5b;", "name": "Radio Empty", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "radio selected circle bullet radio", "unicode": "&#x5c;", "name": "Radio Selected", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "stop", "unicode": "&#x5d;", "name": "Stop", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "stop bullet square", "unicode": "&#x5e;", "name": "Stop", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pause", "unicode": "&#x5f;", "name": "Pause", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pause", "unicode": "&#x60;", "name": "Pause", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "menu hamburger expand open reorder drag", "unicode": "&#x61;", "name": "<PERSON><PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "menu hamburger expand open reorder drag", "unicode": "&#x62;", "name": "<PERSON><PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "menu hamburger expand open reorder drag", "unicode": "&#x63;", "name": "<PERSON><PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "unordered list list unordered", "unicode": "&#x64;", "name": "Unordered List", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "ordered list list numbered orderd", "unicode": "&#x65;", "name": "Ordered List", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "adjustments vertical adjust options settings dial", "unicode": "&#x66;", "name": "Adjustments Vertical", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "adjustments horizontal adjust options settings dial", "unicode": "&#x67;", "name": "Adjustments Horizontal", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "document notes file", "unicode": "&#x68;", "name": "Document", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "documents notes file", "unicode": "&#x69;", "name": "Documents", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pencil edit design write compose", "unicode": "&#x6a;", "name": "Pencil", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pencil edit design write compose", "unicode": "&#x6b;", "name": "Pencil", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pencil edit design write compose", "unicode": "&#x6c;", "name": "Pencil", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "folder", "unicode": "&#x6d;", "name": "Folder", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "open folder folder", "unicode": "&#x6e;", "name": "Open Folder", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "add folder folder", "unicode": "&#x6f;", "name": "Add Folder", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "information help", "unicode": "&#x70;", "name": "Information", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "warning stop caution", "unicode": "&#x71;", "name": "Warning", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "warning caution", "unicode": "&#x72;", "name": "Warning", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "warning caution", "unicode": "&#x73;", "name": "Warning", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "question mark question help", "unicode": "&#x74;", "name": "Question Mark", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "question mark question help", "unicode": "&#x75;", "name": "Question Mark", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "chat speak bubble speech ", "unicode": "&#x76;", "name": "Cha<PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "chat speak bubble speech ", "unicode": "&#x77;", "name": "Cha<PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "volume mute speaker", "unicode": "&#x78;", "name": "Volume Mute", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "volume down volume speaker", "unicode": "&#x79;", "name": "Volume Down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "volume up volume speaker", "unicode": "&#x7a;", "name": "Volume Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "quote testimonial", "unicode": "&#x7b;", "name": "Quote", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "quote testimonial", "unicode": "&#x7c;", "name": "Quote", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "clock time schedule history", "unicode": "&#x7d;", "name": "Clock", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "locked lock security", "unicode": "&#x7e;", "name": "Locked", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "unlocked lock security", "unicode": "&#xe000;", "name": "Unlocked", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "key", "unicode": "&#xe001;", "name": "Key", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cloud hosting backup storage", "unicode": "&#xe002;", "name": "Cloud", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cloud upload cloud hosting backup storage upload", "unicode": "&#xe003;", "name": "Cloud Upload", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cloud download cloud hosting download storage", "unicode": "&#xe004;", "name": "Cloud Download", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "image file media", "unicode": "&#xe005;", "name": "Image", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "images files media portfolio gallery", "unicode": "&#xe006;", "name": "Images", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "lightbulb light idea", "unicode": "&#xe007;", "name": "Lightbulb", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "gift present prize award", "unicode": "&#xe008;", "name": "Gift", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "home house", "unicode": "&#xe009;", "name": "Home", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "atom genius science", "unicode": "&#xe00a;", "name": "Atom", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "phone mobile device", "unicode": "&#xe00b;", "name": "Phone", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tablet mobile device", "unicode": "&#xe00c;", "name": "Tablet", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "laptop computer device", "unicode": "&#xe00d;", "name": "Laptop", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "desktop monitor computer", "unicode": "&#xe00e;", "name": "Desktop", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "camera photography", "unicode": "&#xe00f;", "name": "Camera", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "envelop mail email send envelope", "unicode": "&#xe010;", "name": "Envelop", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cone caution", "unicode": "&#xe011;", "name": "Cone", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "ribbon bookmark", "unicode": "&#xe012;", "name": "Ribbon", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "bag product cart shopping ecommerce", "unicode": "&#xe013;", "name": "Bag", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "credit card card ecommerce shop credit", "unicode": "&#xe014;", "name": "Credit Card", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "shopping cart product cart shopping ecommerce", "unicode": "&#xe015;", "name": "Shopping Cart", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "paper clip attachment file", "unicode": "&#xe016;", "name": "Paper Clip", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tag price sale shop ecommerce discount", "unicode": "&#xe017;", "name": "Tag", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tags tag price sale shop ecommerce discount", "unicode": "&#xe018;", "name": "Tags", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "trashcan trash delete remove bin", "unicode": "&#xe019;", "name": "Trashcan", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cursor click hover ux", "unicode": "&#xe01a;", "name": "<PERSON><PERSON><PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "microphone audio mic record", "unicode": "&#xe01b;", "name": "Microphone", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "compass browser location", "unicode": "&#xe01c;", "name": "<PERSON>mp<PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "map pin pin locatoin address", "unicode": "&#xe01d;", "name": "Map Pin", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "push pin pin", "unicode": "&#xe01e;", "name": "<PERSON><PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "map directions location", "unicode": "&#xe01f;", "name": "Map", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "inbox drawer", "unicode": "&#xe020;", "name": "Inbox", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "toolbox tools box portfolio briefcase", "unicode": "&#xe021;", "name": "Toolbox", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "book", "unicode": "&#xe022;", "name": "Book", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "calendar date event rolodex", "unicode": "&#xe023;", "name": "Calendar", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "film movie video", "unicode": "&#xe024;", "name": "Film", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "table grid", "unicode": "&#xe025;", "name": "Table", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "contacts addresses", "unicode": "&#xe026;", "name": "Contacts", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "headphones audio support", "unicode": "&#xe027;", "name": "Headphones", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "support lifesaver", "unicode": "&#xe028;", "name": "Support", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pie cart pie chart graph statistics", "unicode": "&#xe029;", "name": "<PERSON>", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "redo refresh rotate", "unicode": "&#xe02a;", "name": "Redo", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "link chain connect sync", "unicode": "&#xe02b;", "name": "Link", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "hyperlink link chain connect sync", "unicode": "&#xe02c;", "name": "Hyperlink", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "loading load loader spinner", "unicode": "&#xe02d;", "name": "Loading", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "blocked cancel block none", "unicode": "&#xe02e;", "name": "Blocked", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "box archive", "unicode": "&#xe02f;", "name": "Box", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "heart like love favorite", "unicode": "&#xe030;", "name": "Heart", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "star favorite", "unicode": "&#xe031;", "name": "Star", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "half star star favorite half", "unicode": "&#xe032;", "name": "Half Star", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "printer fax", "unicode": "&#xe103;", "name": "Printer", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "calculator math", "unicode": "&#xe0ee;", "name": "Calculator", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "buildings building business city", "unicode": "&#xe0ef;", "name": "Buildings", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "save floppy disk", "unicode": "&#xe0e8;", "name": "Save", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "hard drive server storage backup", "unicode": "&#xe0ea;", "name": "Hard Drive", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "search document zoom", "unicode": "&#xe101;", "name": "Search Document", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "identity id profile", "unicode": "&#xe107;", "name": "Identity", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "nametag id identity profile tag badge", "unicode": "&#xe108;", "name": "Nametag", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "puzzle strategy", "unicode": "&#xe102;", "name": "Puzzle", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "thumbs up like rate", "unicode": "&#xe106;", "name": "Thumbs Up", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "thumbs down dislike rate", "unicode": "&#xe0eb;", "name": "Thumbs Down", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "coffee mug drink", "unicode": "&#xe105;", "name": "Coffee", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "dollar money currency price", "unicode": "&#xe0ed;", "name": "Dollar", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "wallet money price", "unicode": "&#xe100;", "name": "Wallet", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pens compose pencil pen author", "unicode": "&#xe104;", "name": "Pens", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "presentation easel graph statistics", "unicode": "&#xe0e9;", "name": "Presentation", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "organizational chart graph chart organization", "unicode": "&#xe109;", "name": "Organizational Chart", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "bar chart data chart graph statistics performance", "unicode": "&#xe0ec;", "name": "Bar Chart", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "brief case breifcase portfolio job work", "unicode": "&#xe0fe;", "name": "Brief Case", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "shield warning sheild warning caution security", "unicode": "&#xe0f6;", "name": "Shield Warning", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "percentage sale discount", "unicode": "&#xe0fb;", "name": "Percent", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "globe", "unicode": "&#xe0e2;", "name": "Globe", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "internet globe network", "unicode": "&#xe0e3;", "name": "Internet", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "target", "unicode": "&#xe0f5;", "name": "Target", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "hourglass time", "unicode": "&#xe0e1;", "name": "Hourglass", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "scale balance", "unicode": "&#xe0ff;", "name": "Scale", "styles": ["divi", "line", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "star favorite", "unicode": "&#xe033;", "name": "Star", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "half star star favorite half", "unicode": "&#xe034;", "name": "Half Star", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tools settings", "unicode": "&#xe035;", "name": "Tools", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tools settings", "unicode": "&#xe036;", "name": "Tool", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "gear settings", "unicode": "&#xe037;", "name": "Gear", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "gears gear settings", "unicode": "&#xe038;", "name": "Gears", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow up direction previous collapse", "unicode": "&#xe039;", "name": "Arrow Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow down direction expand next", "unicode": "&#xe03a;", "name": "Arrow Down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow left direction prevous back", "unicode": "&#xe03b;", "name": "Arrow Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow right direction next forward", "unicode": "&#xe03c;", "name": "Arrow Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow up left direction previous back", "unicode": "&#xe03d;", "name": "Arrow Up Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow up right direction next forward", "unicode": "&#xe03e;", "name": "Arrow Up Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow down right direction next forward", "unicode": "&#xe03f;", "name": "Arrow Down Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrow down left direction previous back", "unicode": "&#xe040;", "name": "Arrow Down Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrows condense arrow condense resize collapse close", "unicode": "&#xe041;", "name": "Arrows Condense", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "arrows expand arrow expand resize open", "unicode": "&#xe042;", "name": "Arrows Expand", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret up arrow caret up direction previous collapse", "unicode": "&#xe043;", "name": "Caret Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret down arrow caret down direction expand next", "unicode": "&#xe044;", "name": "Caret down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret left arrow caret left direction prevous back", "unicode": "&#xe045;", "name": "Caret Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "caret right arrow caret right direction next forward", "unicode": "&#xe046;", "name": "Caret Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret up arrow caret up direction previous collapse", "unicode": "&#xe047;", "name": "Double Caret Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret down arrow caret down direction expand next", "unicode": "&#xe048;", "name": "Double Caret down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret left arrow caret left direction prevous back", "unicode": "&#xe049;", "name": "Double Caret Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "double caret right arrow caret right direction next forward", "unicode": "&#xe04a;", "name": "Double Caret Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "solid caret up arrow caret triangle up direction previous collapse", "unicode": "&#xe04b;", "name": "Solid Caret Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "solid caret down arrow caret triangle down direction expand next", "unicode": "&#xe04c;", "name": "Solid Caret down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "solid caret left arrow caret triangle left direction prevous back", "unicode": "&#xe04d;", "name": "Solid Caret Left", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "solid caret right arrow caret triangle right direction next forward", "unicode": "&#xe04e;", "name": "Solid Caret Right", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "minus dash hyphen", "unicode": "&#xe04f;", "name": "Minus", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "plus add cross", "unicode": "&#xe050;", "name": "Plus", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "close x cancel delete remove", "unicode": "&#xe051;", "name": "Close", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "check confirm", "unicode": "&#xe052;", "name": "Check", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "zoom out zoom magnifying glass", "unicode": "&#xe053;", "name": "Zoom Out", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "zoom in zoom magnifying glass", "unicode": "&#xe054;", "name": "Zoom In", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "stop", "unicode": "&#xe055;", "name": "Stop", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "menu hamburger expand open reorder drag", "unicode": "&#xe056;", "name": "<PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "menu hamburger expand open reorder drag", "unicode": "&#xe057;", "name": "<PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "document notes file", "unicode": "&#xe058;", "name": "Document", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "documents notes file", "unicode": "&#xe059;", "name": "Documents", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pencil edit design write compose", "unicode": "&#xe05a;", "name": "Pencil", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "folder", "unicode": "&#xe05b;", "name": "Folder", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "open folder folder", "unicode": "&#xe05c;", "name": "Open Folder", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "add folder folder", "unicode": "&#xe05d;", "name": "Add Folder", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "upload folder folder upload backup", "unicode": "&#xe05e;", "name": "Upload Folder", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "download flder folder download", "unicode": "&#xe05f;", "name": "Download Flder", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "information help", "unicode": "&#xe060;", "name": "Information", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "warning stop caution", "unicode": "&#xe061;", "name": "Warning", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "warning caution", "unicode": "&#xe062;", "name": "Warning", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "warning caution", "unicode": "&#xe063;", "name": "Warning", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "question mark question help", "unicode": "&#xe064;", "name": "Question Mark", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "chat speak bubble speech ", "unicode": "&#xe065;", "name": "Cha<PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "chat speak bubble speech ", "unicode": "&#xe066;", "name": "Cha<PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "volume mute speaker", "unicode": "&#xe067;", "name": "Volume Mute", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "volume down volume speaker", "unicode": "&#xe068;", "name": "Volume Down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "volume up volume speaker", "unicode": "&#xe069;", "name": "Volume Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "quote testimonial", "unicode": "&#xe06a;", "name": "Quote", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "clock time schedule history", "unicode": "&#xe06b;", "name": "Clock", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "locked lock security", "unicode": "&#xe06c;", "name": "Locked", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "unlocked lock security", "unicode": "&#xe06d;", "name": "Unlocked", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "key", "unicode": "&#xe06e;", "name": "Key", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cloud hosting backup storage", "unicode": "&#xe06f;", "name": "Cloud", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cloud upload cloud hosting backup storage upload", "unicode": "&#xe070;", "name": "Cloud Upload", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cloud download cloud hosting download storage", "unicode": "&#xe071;", "name": "Cloud Download", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "lightbulb light idea", "unicode": "&#xe072;", "name": "Lightbulb", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "gift present prize award", "unicode": "&#xe073;", "name": "Gift", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "home house", "unicode": "&#xe074;", "name": "Home", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "camera photography", "unicode": "&#xe075;", "name": "Camera", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "envelop mail email send envelope", "unicode": "&#xe076;", "name": "Envelop", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cone caution", "unicode": "&#xe077;", "name": "Cone", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "ribbon bookmark", "unicode": "&#xe078;", "name": "Ribbon", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "bag product cart shopping ecommerce", "unicode": "&#xe079;", "name": "Bag", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "shopping cart product cart shopping ecommerce", "unicode": "&#xe07a;", "name": "Shopping Cart", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tag price sale shop ecommerce discount", "unicode": "&#xe07b;", "name": "Tag", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tags tag price sale shop ecommerce discount", "unicode": "&#xe07c;", "name": "Tags", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "trashcan trash delete remove bin", "unicode": "&#xe07d;", "name": "Trashcan", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "cursor click hover ux", "unicode": "&#xe07e;", "name": "<PERSON><PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "microphone audio mic record", "unicode": "&#xe07f;", "name": "Microphone", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "compass browser location", "unicode": "&#xe080;", "name": "<PERSON>mp<PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "map pin pin locatoin address", "unicode": "&#xe081;", "name": "Map Pin", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "push pin pin", "unicode": "&#xe082;", "name": "<PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "map directions location", "unicode": "&#xe083;", "name": "Map", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "inbox drawer", "unicode": "&#xe084;", "name": "Inbox", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "toolbox tools box portfolio briefcase", "unicode": "&#xe085;", "name": "Toolbox", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "book", "unicode": "&#xe086;", "name": "Book", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "contacts addresses", "unicode": "&#xe087;", "name": "Contacts", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "box archive", "unicode": "&#xe088;", "name": "Box", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "heart like love favorite", "unicode": "&#xe089;", "name": "Heart", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "person profile avatar", "unicode": "&#xe08a;", "name": "Person", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "group people", "unicode": "&#xe08b;", "name": "Group", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "grid", "unicode": "&#xe08c;", "name": "Grid", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "grid", "unicode": "&#xe08d;", "name": "Grid", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "audio note music sound song", "unicode": "&#xe08e;", "name": "Audio", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pause", "unicode": "&#xe08f;", "name": "Pause", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "phone call", "unicode": "&#xe090;", "name": "Phone", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "upload", "unicode": "&#xe091;", "name": "Upload", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "download", "unicode": "&#xe092;", "name": "Download", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "castle chess strategy rook", "unicode": "&#xe0f8;", "name": "Castle", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "printer fax", "unicode": "&#xe0fa;", "name": "Printer", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "calculator math", "unicode": "&#xe0e7;", "name": "Calculator", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "buildings building business city", "unicode": "&#xe0fd;", "name": "Buildings", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "save floppy disk", "unicode": "&#xe0e4;", "name": "Save", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "hard drive server storage backup", "unicode": "&#xe0e5;", "name": "Hard Drive", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "search document zoom", "unicode": "&#xe0f7;", "name": "Search Document", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "identity id profile", "unicode": "&#xe0e0;", "name": "Identity", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "nametag id identity profile tag badge", "unicode": "&#xe0fc;", "name": "Nametag", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "puzzle strategy", "unicode": "&#xe0f9;", "name": "Puzzle", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "thumbs up like rate", "unicode": "&#xe0dd;", "name": "Thumbs Up", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "thumbs down dislike rate", "unicode": "&#xe0f1;", "name": "Thumbs Down", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "coffee mug drink", "unicode": "&#xe0dc;", "name": "Coffee", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "dollar money currency price", "unicode": "&#xe0f3;", "name": "Dollar", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "wallet money price", "unicode": "&#xe0d8;", "name": "Wallet", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pens compose pencil pen author", "unicode": "&#xe0db;", "name": "Pens", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "presentation easel graph statistics", "unicode": "&#xe0f0;", "name": "Presentation", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "organizational chart graph chart organization", "unicode": "&#xe0df;", "name": "Organizational Chart", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "bar chart data chart graph statistics performance", "unicode": "&#xe0f2;", "name": "Bar Chart", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "brief case breifcase portfolio job work", "unicode": "&#xe0f4;", "name": "Brief Case", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "shield security", "unicode": "&#xe0d9;", "name": "Shield", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "percentage sale discount", "unicode": "&#xe0da;", "name": "Percent", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "globe", "unicode": "&#xe0de;", "name": "Globe", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "clipboard list", "unicode": "&#xe0e6;", "name": "Clipboard", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "facebook social", "unicode": "&#xe093;", "name": "Facebook", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "twitter social", "unicode": "&#xe094;", "name": "Twitter", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pinterest social", "unicode": "&#xe095;", "name": "Pinterest", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "google plus social", "unicode": "&#xe096;", "name": "Google Plus", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tumbler social tumblr", "unicode": "&#xe097;", "name": "Tumbler", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "stumbleupon social", "unicode": "&#xe098;", "name": "StumbleUpon", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "wordpress", "unicode": "&#xe099;", "name": "WordPress", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "instagram social", "unicode": "&#xe09a;", "name": "Instagram", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "dribbble social", "unicode": "&#xe09b;", "name": "<PERSON><PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "venmo", "unicode": "&#xe09c;", "name": "Ven<PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "linkedin social", "unicode": "&#xe09d;", "name": "LinkedIn", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "rss", "unicode": "&#xe09e;", "name": "RSS", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "deviantart social", "unicode": "&#xe09f;", "name": "DeviantArt", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "share social network", "unicode": "&#xe0a0;", "name": "Share", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "myspace social", "unicode": "&#xe0a1;", "name": "MySpace", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "skype", "unicode": "&#xe0a2;", "name": "Skype", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "youtube play video", "unicode": "&#xe0a3;", "name": "YouTube", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "picassa", "unicode": "&#xe0a4;", "name": "Picassa", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "google drive", "unicode": "&#xe0a5;", "name": "Google Drive", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "flickr", "unicode": "&#xe0a6;", "name": "<PERSON>lickr", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "blogger social", "unicode": "&#xe0a7;", "name": "Blogger", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "spotify", "unicode": "&#xe0a8;", "name": "Spotify", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "delicious social", "unicode": "&#xe0a9;", "name": "Delicious", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "facebook social", "unicode": "&#xe0aa;", "name": "Facebook", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "twitter social", "unicode": "&#xe0ab;", "name": "Twitter", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pinterest social", "unicode": "&#xe0ac;", "name": "Pinterest", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "google plus social", "unicode": "&#xe0ad;", "name": "Google Plus", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tumbler social tumblr", "unicode": "&#xe0ae;", "name": "Tumbler", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "stumbleupon social", "unicode": "&#xe0af;", "name": "StumbleUpon", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "wordpress", "unicode": "&#xe0b0;", "name": "WordPress", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "instagram social", "unicode": "&#xe0b1;", "name": "Instagram", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "dribbble social", "unicode": "&#xe0b2;", "name": "<PERSON><PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "venmo", "unicode": "&#xe0b3;", "name": "Ven<PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "linkedin social", "unicode": "&#xe0b4;", "name": "LinkedIn", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "rss", "unicode": "&#xe0b5;", "name": "RSS", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "deviantart social", "unicode": "&#xe0b6;", "name": "DeviantArt", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "share social network", "unicode": "&#xe0b7;", "name": "Share", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "myspace social", "unicode": "&#xe0b8;", "name": "MySpace", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "skype", "unicode": "&#xe0b9;", "name": "Skype", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "youtube play video", "unicode": "&#xe0ba;", "name": "YouTube", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "picassa", "unicode": "&#xe0bb;", "name": "Picassa", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "google drive", "unicode": "&#xe0bc;", "name": "Google Drive", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "flickr", "unicode": "&#xe0bd;", "name": "<PERSON>lickr", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "blogger social", "unicode": "&#xe0be;", "name": "Blogger", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "spotify", "unicode": "&#xe0bf;", "name": "Spotify", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "delicious social", "unicode": "&#xe0c0;", "name": "Delicious", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "facebook social", "unicode": "&#xe0c1;", "name": "Facebook", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "twitter social", "unicode": "&#xe0c2;", "name": "Twitter", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pinterest social", "unicode": "&#xe0c3;", "name": "Pinterest", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "google plus social", "unicode": "&#xe0c4;", "name": "Google Plus", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tumbler social tumblr", "unicode": "&#xe0c5;", "name": "Tumbler", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "stumbleupon social", "unicode": "&#xe0c6;", "name": "StumbleUpon", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "wordpress", "unicode": "&#xe0c7;", "name": "WordPress", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "instagram social", "unicode": "&#xe0c8;", "name": "Instagram", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "dribbble social", "unicode": "&#xe0c9;", "name": "<PERSON><PERSON><PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "venmo", "unicode": "&#xe0ca;", "name": "Ven<PERSON>", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "linkedin social", "unicode": "&#xe0cb;", "name": "LinkedIn", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "rss", "unicode": "&#xe0cc;", "name": "RSS", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "deviantart social", "unicode": "&#xe0cd;", "name": "DeviantArt", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "share social network", "unicode": "&#xe0ce;", "name": "Share", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "myspace social", "unicode": "&#xe0cf;", "name": "MySpace", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "skype", "unicode": "&#xe0d0;", "name": "Skype", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "youtube play video", "unicode": "&#xe0d1;", "name": "YouTube", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "picassa", "unicode": "&#xe0d2;", "name": "Picassa", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "google drive", "unicode": "&#xe0d3;", "name": "Google Drive", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "flickr", "unicode": "&#xe0d4;", "name": "<PERSON>lickr", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "blogger social", "unicode": "&#xe0d5;", "name": "Blogger", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "spotify", "unicode": "&#xe0d6;", "name": "Spotify", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "delicious social", "unicode": "&#xe0d7;", "name": "Delicious", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "accordion toggle list", "unicode": "&#xe600;", "name": "Accordion", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "tabs", "unicode": "&#xe601;", "name": "Tabs", "styles": ["divi", "solid"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "rss", "unicode": "&#xe602;", "name": "RSS", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "slider gallery", "unicode": "&#xe603;", "name": "Slide<PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "sidebar", "unicode": "&#xe604;", "name": "Sidebar", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "share social network", "unicode": "&#xe605;", "name": "Share", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "pricing tables", "unicode": "&#xe606;", "name": "Pricing Tables", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "table grid", "unicode": "&#xe607;", "name": "Table", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "hashtag number pound", "unicode": "&#xe608;", "name": "Hashtag", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "header browser website", "unicode": "&#xe609;", "name": "Header", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "table grid", "unicode": "&#xe60a;", "name": "Table", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "spacer divider expand arrow resize drag", "unicode": "&#xe60b;", "name": "Spacer", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "button link", "unicode": "&#xe60c;", "name": "<PERSON><PERSON>", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "history clock backup time", "unicode": "&#xe60d;", "name": "History", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "percentage counter graph data", "unicode": "&#xe60e;", "name": "Percent", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "quote chat", "unicode": "&#xe60f;", "name": "Quote", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "text list", "unicode": "&#xe610;", "name": "Text", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "audio note music sound song", "unicode": "&#xe611;", "name": "Audio", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "accordion toggle list", "unicode": "&#xe612;", "name": "Accordion", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "code", "unicode": "&#xe620;", "name": "Code", "styles": ["divi", "line"], "is_divi_icon": true, "font_weight": 400}, {"search_terms": "500px", "unicode": "&#xf26e;", "name": "500px", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "accessible-icon accessible icon accessibility handicap person wheelchair wheelchairalt", "unicode": "&#xf368;", "name": "accessible-icon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "accusoft", "unicode": "&#xf369;", "name": "accusoft", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "acquisitions-incorporated acquisitions incorporated dungeons  dragons dd dnd fantasy game gaming tabletop", "unicode": "&#xf6af;", "name": "acquisitions-incorporated", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "advertisement media newspaper promotion publicity", "unicode": "&#xf641;", "name": "ad", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "address-book address book contact directory index little black book rolodex", "unicode": "&#xf2b9;", "name": "address-book", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "address-book address book contact directory index little black book rolodex", "unicode": "&#xf2b9;", "name": "address-book", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "address-card address card about contact id identification postcard profile", "unicode": "&#xf2bb;", "name": "address-card", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "address-card address card about contact id identification postcard profile", "unicode": "&#xf2bb;", "name": "address-card", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "adjust contrast dark light saturation", "unicode": "&#xf042;", "name": "adjust", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "adnapp.net", "unicode": "&#xf170;", "name": "adn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "adversal", "unicode": "&#xf36a;", "name": "adversal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "affiliatetheme", "unicode": "&#xf36b;", "name": "affiliatetheme", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "air-freshener air freshener car deodorize fresh pine scent", "unicode": "&#xf5d0;", "name": "air-freshener", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "airbnb", "unicode": "&#xf834;", "name": "airbnb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "algolia", "unicode": "&#xf36c;", "name": "algolia", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "align-center format middle paragraph text", "unicode": "&#xf037;", "name": "align-center", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "align-justify format paragraph text", "unicode": "&#xf039;", "name": "align-justify", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "align-left format paragraph text", "unicode": "&#xf036;", "name": "align-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "align-right format paragraph text", "unicode": "&#xf038;", "name": "align-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "alipay", "unicode": "&#xf642;", "name": "alipay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "allergies allergy freckles hand hives pox skin spots", "unicode": "&#xf461;", "name": "allergies", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "amazon", "unicode": "&#xf270;", "name": "amazon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "amazon-payamazon pay", "unicode": "&#xf42c;", "name": "amazon-pay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ambulance covid19 emergency emt er help hospital support vehicle", "unicode": "&#xf0f9;", "name": "ambulance", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "american-sign-language-interpreting american sign language interpreting asl deaf finger hand interpret speak", "unicode": "&#xf2a3;", "name": "american-sign-language-interpreting", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "amilia", "unicode": "&#xf36d;", "name": "amilia", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "anchor berth boat dock embed link maritime moor secure", "unicode": "&#xf13d;", "name": "anchor", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "android robot", "unicode": "&#xf17b;", "name": "android", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "angellist", "unicode": "&#xf209;", "name": "angellist", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "angle-double-down angle double down arrows caret download expand", "unicode": "&#xf103;", "name": "angle-double-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-double-left angle double left arrows back caret laquo previous quote", "unicode": "&#xf100;", "name": "angle-double-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-double-right angle double right arrows caret forward more next quote raquo", "unicode": "&#xf101;", "name": "angle-double-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-double-up angle double up arrows caret collapse upload", "unicode": "&#xf102;", "name": "angle-double-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-down arrow caret download expand", "unicode": "&#xf107;", "name": "angle-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-left arrow back caret less previous", "unicode": "&#xf104;", "name": "angle-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-right arrow care forward more next", "unicode": "&#xf105;", "name": "angle-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angle-up arrow caret collapse upload", "unicode": "&#xf106;", "name": "angle-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angry face disapprove emoticon face mad upset", "unicode": "&#xf556;", "name": "angry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "angry face disapprove emoticon face mad upset", "unicode": "&#xf556;", "name": "angry", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "angrycreative<PERSON>ry creative", "unicode": "&#xf36e;", "name": "angrycreative", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "angular", "unicode": "&#xf420;", "name": "angular", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ankh amulet copper coptic christianity copts crux ansata egypt venus", "unicode": "&#xf644;", "name": "ankh", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "app-storeapp store", "unicode": "&#xf36f;", "name": "app-store", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "app-store-iosios app store", "unicode": "&#xf370;", "name": "app-store-ios", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "apper systems ab", "unicode": "&#xf371;", "name": "apper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "apple fruit ios mac operating system os osx", "unicode": "&#xf179;", "name": "apple", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "apple-alt fruit apple fall fruit fuji macintosh orchard seasonal vegan", "unicode": "&#xf5d1;", "name": "apple-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "apple-payapple pay", "unicode": "&#xf415;", "name": "apple-pay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "archive box package save storage", "unicode": "&#xf187;", "name": "archive", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "archway arc monument road street tunnel", "unicode": "&#xf557;", "name": "archway", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-alt-circle-down alternate arrow circle down arrowcircleodown download", "unicode": "&#xf358;", "name": "arrow-alt-circle-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-alt-circle-down alternate arrow circle down arrowcircleodown download", "unicode": "&#xf358;", "name": "arrow-alt-circle-down", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "arrow-alt-circle-left alternate arrow circle left arrowcircleoleft back previous", "unicode": "&#xf359;", "name": "arrow-alt-circle-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-alt-circle-left alternate arrow circle left arrowcircleoleft back previous", "unicode": "&#xf359;", "name": "arrow-alt-circle-left", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "arrow-alt-circle-right alternate arrow circle right arrowcircleoright forward next", "unicode": "&#xf35a;", "name": "arrow-alt-circle-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-alt-circle-right alternate arrow circle right arrowcircleoright forward next", "unicode": "&#xf35a;", "name": "arrow-alt-circle-right", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "arrow-alt-circle-up alternate arrow circle up arrowcircleoup", "unicode": "&#xf35b;", "name": "arrow-alt-circle-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-alt-circle-up alternate arrow circle up arrowcircleoup", "unicode": "&#xf35b;", "name": "arrow-alt-circle-up", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "arrow-circle-down arrow circle down download", "unicode": "&#xf0ab;", "name": "arrow-circle-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-circle-left arrow circle left back previous", "unicode": "&#xf0a8;", "name": "arrow-circle-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-circle-right arrow circle right forward next", "unicode": "&#xf0a9;", "name": "arrow-circle-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-circle-up arrow circle up upload", "unicode": "&#xf0aa;", "name": "arrow-circle-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-down download", "unicode": "&#xf063;", "name": "arrow-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-left back previous", "unicode": "&#xf060;", "name": "arrow-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-right forward next", "unicode": "&#xf061;", "name": "arrow-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrow-up forward upload", "unicode": "&#xf062;", "name": "arrow-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrows-alt alternate arrows arrow arrows bigger enlarge expand fullscreen move position reorder resize", "unicode": "&#xf0b2;", "name": "arrows-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrows-alt-h alternate arrows horizontal arrowsh expand horizontal landscape resize wide", "unicode": "&#xf337;", "name": "arrows-alt-h", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "arrows-alt-v alternate arrows vertical arrowsv expand portrait resize tall vertical", "unicode": "&#xf338;", "name": "arrows-alt-v", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "artstation", "unicode": "&#xf77a;", "name": "artstation", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "assistive-listening-systems assistive listening systems amplify audio deaf ear headset hearing sound", "unicode": "&#xf2a2;", "name": "assistive-listening-systems", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "asterisk annotation details reference star", "unicode": "&#xf069;", "name": "asterisk", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "asymmetrik, ltd.", "unicode": "&#xf372;", "name": "asymmetrik", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "at address author email email handle", "unicode": "&#xf1fa;", "name": "at", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "atlas book directions geography globe map travel wayfinding", "unicode": "&#xf558;", "name": "atlas", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "atlassian", "unicode": "&#xf77b;", "name": "atlassian", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "atom atheism chemistry electron ion isotope neutron nuclear proton science", "unicode": "&#xf5d2;", "name": "atom", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "audible", "unicode": "&#xf373;", "name": "audible", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "audio-description audio description blind narration video visual", "unicode": "&#xf29e;", "name": "audio-description", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "autoprefixer", "unicode": "&#xf41c;", "name": "autoprefixer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "avianex", "unicode": "&#xf374;", "name": "avianex", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "aviato", "unicode": "&#xf421;", "name": "aviato", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "award honor praise prize recognition ribbon trophy", "unicode": "&#xf559;", "name": "award", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "amazon web services (aws)", "unicode": "&#xf375;", "name": "aws", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "baby child diaper doll human infant kid offspring person sprout", "unicode": "&#xf77c;", "name": "baby", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "baby-carriage baby carriage buggy carrier infant push stroller transportation walk wheels", "unicode": "&#xf77d;", "name": "baby-carriage", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "backspace command delete erase keyboard undo", "unicode": "&#xf55a;", "name": "backspace", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "backward previous rewind", "unicode": "&#xf04a;", "name": "backward", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bacon blt breakfast ham lard meat pancetta pork rasher", "unicode": "&#xf7e5;", "name": "bacon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bacteria antibiotic antibody covid19 health organism sick", "unicode": "&#xe059;", "name": "bacteria", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bacterium antibiotic antibody covid19 health organism sick", "unicode": "&#xe05a;", "name": "bacterium", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bah<PERSON>'í bahai bah star", "unicode": "&#xf666;", "name": "bahai", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "balance-scale balance scale balanced justice legal measure weight", "unicode": "&#xf24e;", "name": "balance-scale", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "balance-scale-left balance scale (left-weighted) justice legal measure unbalanced weight", "unicode": "&#xf515;", "name": "balance-scale-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "balance-scale-right balance scale (right-weighted) justice legal measure unbalanced weight", "unicode": "&#xf516;", "name": "balance-scale-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ban abort block cancel delete hide prohibit remove stop trash", "unicode": "&#xf05e;", "name": "ban", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "band-aid bandage boo boo first aid ouch", "unicode": "&#xf462;", "name": "band-aid", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bandcamp", "unicode": "&#xf2d5;", "name": "bandcamp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "barcode info laser price scan upc", "unicode": "&#xf02a;", "name": "barcode", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bars checklist drag hamburger list menu nav navigation ol reorder settings todo ul", "unicode": "&#xf0c9;", "name": "bars", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "baseball-ball baseball ball foul hardball league leather mlb softball sport", "unicode": "&#xf433;", "name": "baseball-ball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "basketball-ball basketball ball dribble dunk hoop nba", "unicode": "&#xf434;", "name": "basketball-ball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bath clean shower tub wash", "unicode": "&#xf2cd;", "name": "bath", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "battery-empty battery empty charge dead power status", "unicode": "&#xf244;", "name": "battery-empty", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "battery-full battery full charge power status", "unicode": "&#xf240;", "name": "battery-full", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "battery-half battery 1/2 full charge power status", "unicode": "&#xf242;", "name": "battery-half", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "battery-quarter battery 1/4 full charge low power status", "unicode": "&#xf243;", "name": "battery-quarter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "battery-three-quarters battery 3/4 full charge power status", "unicode": "&#xf241;", "name": "battery-three-quarters", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "battle-netbattle.net", "unicode": "&#xf835;", "name": "battle-net", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bed lodging mattress rest sleep travel", "unicode": "&#xf236;", "name": "bed", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "beer alcohol ale bar beverage brewery drink lager liquor mug stein", "unicode": "&#xf0fc;", "name": "beer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "behance", "unicode": "&#xf1b4;", "name": "behance", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "behance-squarebehance square", "unicode": "&#xf1b5;", "name": "behance-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bell alarm alert chime notification reminder", "unicode": "&#xf0f3;", "name": "bell", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bell alarm alert chime notification reminder", "unicode": "&#xf0f3;", "name": "bell", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "bell-slash bell slash alert cancel disabled notification off reminder", "unicode": "&#xf1f6;", "name": "bell-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bell-slash bell slash alert cancel disabled notification off reminder", "unicode": "&#xf1f6;", "name": "bell-slash", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "bezier-curve bezier curve curves illustrator lines path vector", "unicode": "&#xf55b;", "name": "bezier-curve", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bible book catholicism christianity god holy", "unicode": "&#xf647;", "name": "bible", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bicycle bike gears pedal transportation vehicle", "unicode": "&#xf206;", "name": "bicycle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "biking bicycle bike cycle cycling ride wheel", "unicode": "&#xf84a;", "name": "biking", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bimobject", "unicode": "&#xf378;", "name": "bimobject", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "binoculars glasses magnify scenic spyglass view", "unicode": "&#xf1e5;", "name": "binoculars", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "biohazard covid19 danger dangerous hazmat medical radioactive toxic waste zombie", "unicode": "&#xf780;", "name": "biohazard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "birthday-cake birthday cake anniversary bakery candles celebration dessert frosting holiday party pastry", "unicode": "&#xf1fd;", "name": "birthday-cake", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bitbucket atlassian bitbucketsquare git", "unicode": "&#xf171;", "name": "bitbucket", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bitcoin", "unicode": "&#xf379;", "name": "bitcoin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bity", "unicode": "&#xf37a;", "name": "bity", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "black-tiefont awesome black tie", "unicode": "&#xf27e;", "name": "black-tie", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "blackberry", "unicode": "&#xf37b;", "name": "blackberry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "blender cocktail milkshake mixer puree smoothie", "unicode": "&#xf517;", "name": "blender", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "blender-phone blender phone appliance cocktail communication fantasy milkshake mixer puree silly smoothie", "unicode": "&#xf6b6;", "name": "blender-phone", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "blind cane disability person sight", "unicode": "&#xf29d;", "name": "blind", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "blog journal log online personal post web 20 wordpress writing", "unicode": "&#xf781;", "name": "blog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "blogger", "unicode": "&#xf37c;", "name": "blogger", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "blogger-bblogger b", "unicode": "&#xf37d;", "name": "blogger-b", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bluetooth", "unicode": "&#xf293;", "name": "bluetooth", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bluetooth-bbluetooth", "unicode": "&#xf294;", "name": "bluetooth-b", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bold emphasis format text", "unicode": "&#xf032;", "name": "bold", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lightning bolt electricity lightning weather zap", "unicode": "&#xf0e7;", "name": "bolt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bomb error explode fuse grenade warning", "unicode": "&#xf1e2;", "name": "bomb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bone calcium dog skeletal skeleton tibia", "unicode": "&#xf5d7;", "name": "bone", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bong aparatus cannabis marijuana pipe smoke smoking", "unicode": "&#xf55c;", "name": "bong", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "book diary documentation journal library read", "unicode": "&#xf02d;", "name": "book", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "book-dead book of the dead dungeons  dragons crossbones dd dark arts death dnd documentation evil fantasy halloween holiday necronomicon read skull spell", "unicode": "&#xf6b7;", "name": "book-dead", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "book-medical medical book diary documentation health history journal library read record", "unicode": "&#xf7e6;", "name": "book-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "book-open book open flyer library notebook open book pamphlet reading", "unicode": "&#xf518;", "name": "book-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "book-reader book reader flyer library notebook open book pamphlet reading", "unicode": "&#xf5da;", "name": "book-reader", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bookmark favorite marker read remember save", "unicode": "&#xf02e;", "name": "bookmark", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bookmark favorite marker read remember save", "unicode": "&#xf02e;", "name": "bookmark", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "bootstrap", "unicode": "&#xf836;", "name": "bootstrap", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "border-all border all cell grid outline stroke table", "unicode": "&#xf84c;", "name": "border-all", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "border-none border none cell grid outline stroke table", "unicode": "&#xf850;", "name": "border-none", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "border-styleborder style", "unicode": "&#xf853;", "name": "border-style", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bowling-ball bowling ball alley candlepin gutter lane strike tenpin", "unicode": "&#xf436;", "name": "bowling-ball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "box archive container package storage", "unicode": "&#xf466;", "name": "box", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "box-open box open archive container package storage unpack", "unicode": "&#xf49e;", "name": "box-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "box-tissue tissue box cough covid19 kleenex mucus nose sneeze snot", "unicode": "&#xe05b;", "name": "box-tissue", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "boxes archives inventory storage warehouse", "unicode": "&#xf468;", "name": "boxes", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "braille alphabet blind dots raised vision", "unicode": "&#xf2a1;", "name": "braille", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "brain cerebellum gray matter intellect medulla oblongata mind noodle wit", "unicode": "&#xf5dc;", "name": "brain", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bread-slice bread slice bake bakery baking dough flour gluten grain sandwich sourdough toast wheat yeast", "unicode": "&#xf7ec;", "name": "bread-slice", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "briefcase bag business luggage office work", "unicode": "&#xf0b1;", "name": "briefcase", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "briefcase-medical medical briefcase doctor emt first aid health", "unicode": "&#xf469;", "name": "briefcase-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "broadcast-tower broadcast tower airwaves antenna radio reception waves", "unicode": "&#xf519;", "name": "broadcast-tower", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "broom clean firebolt fly halloween nimbus 2000 quidditch sweep witch", "unicode": "&#xf51a;", "name": "broom", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "brush art bristles color handle paint", "unicode": "&#xf55d;", "name": "brush", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "btc", "unicode": "&#xf15a;", "name": "btc", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "buffer", "unicode": "&#xf837;", "name": "buffer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bug beetle error insect report", "unicode": "&#xf188;", "name": "bug", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "building apartment business city company office work", "unicode": "&#xf1ad;", "name": "building", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "building apartment business city company office work", "unicode": "&#xf1ad;", "name": "building", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "bullhorn announcement broadcast louder megaphone share", "unicode": "&#xf0a1;", "name": "bullhorn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bullseye archery goal objective target", "unicode": "&#xf140;", "name": "bullseye", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "burn caliente energy fire flame gas heat hot", "unicode": "&#xf46a;", "name": "burn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "buromobelexpertebüromöbel-experte gmbh & co. kg.", "unicode": "&#xf37f;", "name": "buromobelexperte", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "bus public transportation transportation travel vehicle", "unicode": "&#xf207;", "name": "bus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "bus-alt bus alt mta public transportation transportation travel vehicle", "unicode": "&#xf55e;", "name": "bus-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "business-time business time alarm briefcase business socks clock flight of the conchords reminder wednesday", "unicode": "&#xf64a;", "name": "business-time", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "buy-n-largebuy n large", "unicode": "&#xf8a6;", "name": "buy-n-large", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "buysellads", "unicode": "&#xf20d;", "name": "buysellads", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "calculator abacus addition arithmetic counting math multiplication subtraction", "unicode": "&#xf1ec;", "name": "calculator", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendaro date event schedule time when", "unicode": "&#xf133;", "name": "calendar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendaro date event schedule time when", "unicode": "&#xf133;", "name": "calendar", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "calendar-alt alternate calendar calendar date event schedule time when", "unicode": "&#xf073;", "name": "calendar-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendar-alt alternate calendar calendar date event schedule time when", "unicode": "&#xf073;", "name": "calendar-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "calendar-check calendar check accept agree appointment confirm correct date done event ok schedule select success tick time todo when", "unicode": "&#xf274;", "name": "calendar-check", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendar-check calendar check accept agree appointment confirm correct date done event ok schedule select success tick time todo when", "unicode": "&#xf274;", "name": "calendar-check", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "calendar-day calendar with day focus date detail event focus schedule single day time today when", "unicode": "&#xf783;", "name": "calendar-day", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendar-minus calendar minus calendar date delete event negative remove schedule time when", "unicode": "&#xf272;", "name": "calendar-minus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendar-minus calendar minus calendar date delete event negative remove schedule time when", "unicode": "&#xf272;", "name": "calendar-minus", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "calendar-plus calendar plus add calendar create date event new positive schedule time when", "unicode": "&#xf271;", "name": "calendar-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendar-plus calendar plus add calendar create date event new positive schedule time when", "unicode": "&#xf271;", "name": "calendar-plus", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "calendar-times calendar times archive calendar date delete event remove schedule time when x", "unicode": "&#xf273;", "name": "calendar-times", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "calendar-times calendar times archive calendar date delete event remove schedule time when x", "unicode": "&#xf273;", "name": "calendar-times", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "calendar-week calendar with week focus date detail event focus schedule single week time today when", "unicode": "&#xf784;", "name": "calendar-week", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "camera image lens photo picture record shutter video", "unicode": "&#xf030;", "name": "camera", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "camera-retro retro camera image lens photo picture record shutter video", "unicode": "&#xf083;", "name": "camera-retro", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "campground camping fall outdoors teepee tent tipi", "unicode": "&#xf6bb;", "name": "campground", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "canadian-maple-leaf canadian maple leaf canada flag flora nature plant", "unicode": "&#xf785;", "name": "canadian-maple-leaf", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "candy-cane candy cane candy christmas holiday mint peppermint striped xmas", "unicode": "&#xf786;", "name": "candy-cane", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cannabis bud chronic drugs endica endo ganja marijuana mary jane pot reefer sativa spliff weed whackytabacky", "unicode": "&#xf55f;", "name": "cannabis", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "capsules drugs medicine pills prescription", "unicode": "&#xf46b;", "name": "capsules", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "car auto automobile sedan transportation travel vehicle", "unicode": "&#xf1b9;", "name": "car", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "car-alt alternate car auto automobile sedan transportation travel vehicle", "unicode": "&#xf5de;", "name": "car-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "car-battery car battery auto electric mechanic power", "unicode": "&#xf5df;", "name": "car-battery", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "car-crash car crash accident auto automobile insurance sedan transportation vehicle wreck", "unicode": "&#xf5e1;", "name": "car-crash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "car-side car side auto automobile sedan transportation travel vehicle", "unicode": "&#xf5e4;", "name": "car-side", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caravan camper motor home rv trailer travel", "unicode": "&#xf8ff;", "name": "caravan", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-down caret down arrow dropdown expand menu more triangle", "unicode": "&#xf0d7;", "name": "caret-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-left caret left arrow back previous triangle", "unicode": "&#xf0d9;", "name": "caret-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-right caret right arrow forward next triangle", "unicode": "&#xf0da;", "name": "caret-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-square-down caret square down arrow caretsquareodown dropdown expand menu more triangle", "unicode": "&#xf150;", "name": "caret-square-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-square-down caret square down arrow caretsquareodown dropdown expand menu more triangle", "unicode": "&#xf150;", "name": "caret-square-down", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "caret-square-left caret square left arrow back caretsquareoleft previous triangle", "unicode": "&#xf191;", "name": "caret-square-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-square-left caret square left arrow back caretsquareoleft previous triangle", "unicode": "&#xf191;", "name": "caret-square-left", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "caret-square-right caret square right arrow caretsquareoright forward next triangle", "unicode": "&#xf152;", "name": "caret-square-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-square-right caret square right arrow caretsquareoright forward next triangle", "unicode": "&#xf152;", "name": "caret-square-right", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "caret-square-up caret square up arrow caretsquareoup collapse triangle upload", "unicode": "&#xf151;", "name": "caret-square-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "caret-square-up caret square up arrow caretsquareoup collapse triangle upload", "unicode": "&#xf151;", "name": "caret-square-up", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "caret-up caret up arrow collapse triangle", "unicode": "&#xf0d8;", "name": "caret-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "carrot bugs bunny orange vegan vegetable", "unicode": "&#xf787;", "name": "carrot", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cart-arrow-down shopping cart arrow down download save shopping", "unicode": "&#xf218;", "name": "cart-arrow-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cart-plus add to shopping cart add create new positive shopping", "unicode": "&#xf217;", "name": "cart-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cash-register cash register buy chaching change checkout commerce leaerboard machine pay payment purchase store", "unicode": "&#xf788;", "name": "cash-register", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cat feline halloween holiday kitten kitty meow pet", "unicode": "&#xf6be;", "name": "cat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cc-amazon-payamazon pay credit card", "unicode": "&#xf42d;", "name": "cc-amazon-pay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-amex american express credit card amex", "unicode": "&#xf1f3;", "name": "cc-amex", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-apple-payapple pay credit card", "unicode": "&#xf416;", "name": "cc-apple-pay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-diners-clubdiner's club credit card", "unicode": "&#xf24c;", "name": "cc-diners-club", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-discoverdiscover credit card", "unicode": "&#xf1f2;", "name": "cc-discover", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-jcbjcb credit card", "unicode": "&#xf24b;", "name": "cc-jcb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-mastercardmastercard credit card", "unicode": "&#xf1f1;", "name": "cc-mastercard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-paypalpaypal credit card", "unicode": "&#xf1f4;", "name": "cc-paypal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-stripestripe credit card", "unicode": "&#xf1f5;", "name": "cc-stripe", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cc-visavisa credit card", "unicode": "&#xf1f0;", "name": "cc-visa", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "centercode", "unicode": "&#xf380;", "name": "centercode", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "centos linux operating system os", "unicode": "&#xf789;", "name": "centos", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "certificate badge star verified", "unicode": "&#xf0a3;", "name": "certificate", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chair furniture seat sit", "unicode": "&#xf6c0;", "name": "chair", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chalkboard blackboard learning school teaching whiteboard writing", "unicode": "&#xf51b;", "name": "chalkboard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chalkboard-teacher chalkboard teacher blackboard instructor learning professor school whiteboard writing", "unicode": "&#xf51c;", "name": "chalkboard-teacher", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "charging-station charging station electric ev tesla vehicle", "unicode": "&#xf5e7;", "name": "charging-station", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chart-area area chart analytics area chart graph", "unicode": "&#xf1fe;", "name": "chart-area", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chart-bar bar chart analytics bar chart graph", "unicode": "&#xf080;", "name": "chart-bar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chart-bar bar chart analytics bar chart graph", "unicode": "&#xf080;", "name": "chart-bar", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "chart-line line chart activity analytics chart dashboard gain graph increase line", "unicode": "&#xf201;", "name": "chart-line", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chart-pie pie chart analytics chart diagram graph pie", "unicode": "&#xf200;", "name": "chart-pie", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "check accept agree checkmark confirm correct done notice notification notify ok select success tick todo yes", "unicode": "&#xf00c;", "name": "check", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "check-circle check circle accept agree confirm correct done ok select success tick todo yes", "unicode": "&#xf058;", "name": "check-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "check-circle check circle accept agree confirm correct done ok select success tick todo yes", "unicode": "&#xf058;", "name": "check-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "check-double double check accept agree checkmark confirm correct done notice notification notify ok select success tick todo", "unicode": "&#xf560;", "name": "check-double", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "check-square check square accept agree checkmark confirm correct done ok select success tick todo yes", "unicode": "&#xf14a;", "name": "check-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "check-square check square accept agree checkmark confirm correct done ok select success tick todo yes", "unicode": "&#xf14a;", "name": "check-square", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "cheese cheddar curd gouda melt parmesan sandwich swiss wedge", "unicode": "&#xf7ef;", "name": "cheese", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess board castle checkmate game king rook strategy tournament", "unicode": "&#xf439;", "name": "chess", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-bishop chess bishop board checkmate game strategy", "unicode": "&#xf43a;", "name": "chess-bishop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-board chess board board checkmate game strategy", "unicode": "&#xf43c;", "name": "chess-board", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-king chess king board checkmate game strategy", "unicode": "&#xf43f;", "name": "chess-king", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-knight chess knight board checkmate game horse strategy", "unicode": "&#xf441;", "name": "chess-knight", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-pawn chess pawn board checkmate game strategy", "unicode": "&#xf443;", "name": "chess-pawn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-queen chess queen board checkmate game strategy", "unicode": "&#xf445;", "name": "chess-queen", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chess-rook chess rook board castle checkmate game strategy", "unicode": "&#xf447;", "name": "chess-rook", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-circle-down chevron circle down arrow download dropdown menu more", "unicode": "&#xf13a;", "name": "chevron-circle-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-circle-left chevron circle left arrow back previous", "unicode": "&#xf137;", "name": "chevron-circle-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-circle-right chevron circle right arrow forward next", "unicode": "&#xf138;", "name": "chevron-circle-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-circle-up chevron circle up arrow collapse upload", "unicode": "&#xf139;", "name": "chevron-circle-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-down arrow download expand", "unicode": "&#xf078;", "name": "chevron-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-left arrow back bracket previous", "unicode": "&#xf053;", "name": "chevron-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-right arrow bracket forward next", "unicode": "&#xf054;", "name": "chevron-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chevron-up arrow collapse upload", "unicode": "&#xf077;", "name": "chevron-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "child boy girl kid toddler young", "unicode": "&#xf1ae;", "name": "child", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "chrome browser", "unicode": "&#xf268;", "name": "chrome", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "chromecast", "unicode": "&#xf838;", "name": "chromecast", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "church building cathedral chapel community religion", "unicode": "&#xf51d;", "name": "church", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "circlethin diameter dot ellipse notification round", "unicode": "&#xf111;", "name": "circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "circlethin diameter dot ellipse notification round", "unicode": "&#xf111;", "name": "circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "circle-notch circle notched circleonotch diameter dot ellipse round spinner", "unicode": "&#xf1ce;", "name": "circle-notch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "city buildings busy skyscrapers urban windows", "unicode": "&#xf64f;", "name": "city", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clinic-medical medical clinic covid19 doctor general practitioner hospital infirmary medicine office outpatient", "unicode": "&#xf7f2;", "name": "clinic-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clipboard copy notes paste record", "unicode": "&#xf328;", "name": "clipboard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clipboard copy notes paste record", "unicode": "&#xf328;", "name": "clipboard", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "clipboard-check clipboard with check accept agree confirm done ok select success tick todo yes", "unicode": "&#xf46c;", "name": "clipboard-check", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clipboard-list clipboard list checklist completed done finished intinerary ol schedule tick todo ul", "unicode": "&#xf46d;", "name": "clipboard-list", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clock date late schedule time timer timestamp watch", "unicode": "&#xf017;", "name": "clock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clock date late schedule time timer timestamp watch", "unicode": "&#xf017;", "name": "clock", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "clone arrange copy duplicate paste", "unicode": "&#xf24d;", "name": "clone", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "clone arrange copy duplicate paste", "unicode": "&#xf24d;", "name": "clone", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "closed-captioning closed captioning cc deaf hearing subtitle subtitling text video", "unicode": "&#xf20a;", "name": "closed-captioning", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "closed-captioning closed captioning cc deaf hearing subtitle subtitling text video", "unicode": "&#xf20a;", "name": "closed-captioning", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "cloud atmosphere fog overcast save upload weather", "unicode": "&#xf0c2;", "name": "cloud", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-download-alt alternate cloud download download export save", "unicode": "&#xf381;", "name": "cloud-download-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-meatball cloud with (a chance of) meatball fldsmdfr food spaghetti storm", "unicode": "&#xf73b;", "name": "cloud-meatball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-moon cloud with moon crescent evening lunar night partly cloudy sky", "unicode": "&#xf6c3;", "name": "cloud-moon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-moon-rain cloud with moon and rain crescent evening lunar night partly cloudy precipitation rain sky storm", "unicode": "&#xf73c;", "name": "cloud-moon-rain", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-rain cloud with rain precipitation rain sky storm", "unicode": "&#xf73d;", "name": "cloud-rain", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-showers-heavy cloud with heavy showers precipitation rain sky storm", "unicode": "&#xf740;", "name": "cloud-showers-heavy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-sun cloud with sun clear day daytime fall outdoors overcast partly cloudy", "unicode": "&#xf6c4;", "name": "cloud-sun", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-sun-rain cloud with sun and rain day overcast precipitation storm summer sunshower", "unicode": "&#xf743;", "name": "cloud-sun-rain", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloud-upload-alt alternate cloud upload cloudupload import save upload", "unicode": "&#xf382;", "name": "cloud-upload-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cloudflare", "unicode": "&#xe07d;", "name": "cloudflare", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cloudscale.ch", "unicode": "&#xf383;", "name": "cloudscale", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cloudsmith", "unicode": "&#xf384;", "name": "cloudsmith", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cloudversify", "unicode": "&#xf385;", "name": "cloudversify", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cocktail alcohol beverage drink gin glass margarita martini vodka", "unicode": "&#xf561;", "name": "cocktail", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "code brackets development html", "unicode": "&#xf121;", "name": "code", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "code-branch code branch branch codefork fork git github rebase svn vcs version", "unicode": "&#xf126;", "name": "code-branch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "codepen", "unicode": "&#xf1cb;", "name": "codepen", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "codiepiecodie pie", "unicode": "&#xf284;", "name": "codiepie", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "coffee beverage breakfast cafe drink fall morning mug seasonal tea", "unicode": "&#xf0f4;", "name": "coffee", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cog gear mechanical settings sprocket wheel", "unicode": "&#xf013;", "name": "cog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cogs gears mechanical settings sprocket wheel", "unicode": "&#xf085;", "name": "cogs", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "coins currency dime financial gold money penny", "unicode": "&#xf51e;", "name": "coins", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "columns browser dashboard organize panes split", "unicode": "&#xf0db;", "name": "columns", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comment bubble chat commenting conversation feedback message note notification sms speech texting", "unicode": "&#xf075;", "name": "comment", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comment bubble chat commenting conversation feedback message note notification sms speech texting", "unicode": "&#xf075;", "name": "comment", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "comment-alt alternate comment bubble chat commenting conversation feedback message note notification sms speech texting", "unicode": "&#xf27a;", "name": "comment-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comment-alt alternate comment bubble chat commenting conversation feedback message note notification sms speech texting", "unicode": "&#xf27a;", "name": "comment-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "comment-dollar comment dollar bubble chat commenting conversation feedback message money note notification pay sms speech spend texting transfer", "unicode": "&#xf651;", "name": "comment-dollar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comment-dots comment dots bubble chat commenting conversation feedback message more note notification reply sms speech texting", "unicode": "&#xf4ad;", "name": "comment-dots", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comment-dots comment dots bubble chat commenting conversation feedback message more note notification reply sms speech texting", "unicode": "&#xf4ad;", "name": "comment-dots", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "comment-medical alternate medical chat advice bubble chat commenting conversation diagnose feedback message note notification prescription sms speech texting", "unicode": "&#xf7f5;", "name": "comment-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comment-slash comment slash bubble cancel chat commenting conversation feedback message mute note notification quiet sms speech texting", "unicode": "&#xf4b3;", "name": "comment-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comments bubble chat commenting conversation feedback message note notification sms speech texting", "unicode": "&#xf086;", "name": "comments", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "comments bubble chat commenting conversation feedback message note notification sms speech texting", "unicode": "&#xf086;", "name": "comments", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "comments-dollar comments dollar bubble chat commenting conversation feedback message money note notification pay sms speech spend texting transfer", "unicode": "&#xf653;", "name": "comments-dollar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "compact-disc compact disc album bluray cd disc dvd media movie music record video vinyl", "unicode": "&#xf51f;", "name": "compact-disc", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "compass directions directory location menu navigation safari travel", "unicode": "&#xf14e;", "name": "compass", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "compass directions directory location menu navigation safari travel", "unicode": "&#xf14e;", "name": "compass", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "compress collapse fullscreen minimize move resize shrink smaller", "unicode": "&#xf066;", "name": "compress", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "compress-alt alternate compress collapse fullscreen minimize move resize shrink smaller", "unicode": "&#xf422;", "name": "compress-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "compress-arrows-alt alternate compress arrows collapse fullscreen minimize move resize shrink smaller", "unicode": "&#xf78c;", "name": "compress-arrows-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "concierge-bell concierge bell attention hotel receptionist service support", "unicode": "&#xf562;", "name": "concierge-bell", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "confluence atlassian", "unicode": "&#xf78d;", "name": "confluence", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "connectdevelopconnect develop", "unicode": "&#xf20e;", "name": "connectdevelop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "contao", "unicode": "&#xf26d;", "name": "contao", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cookie baked good chips chocolate eat snack sweet treat", "unicode": "&#xf563;", "name": "cookie", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cookie-bite cookie bite baked good bitten chips chocolate eat snack sweet treat", "unicode": "&#xf564;", "name": "cookie-bite", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "copy clone duplicate file fileso paper paste", "unicode": "&#xf0c5;", "name": "copy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "copy clone duplicate file fileso paper paste", "unicode": "&#xf0c5;", "name": "copy", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "copyright brand mark register trademark", "unicode": "&#xf1f9;", "name": "copyright", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "copyright brand mark register trademark", "unicode": "&#xf1f9;", "name": "copyright", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "cotton-bureau cotton bureau clothing tshirts tshirts", "unicode": "&#xf89e;", "name": "cotton-bureau", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "couch chair cushion furniture relax sofa", "unicode": "&#xf4b8;", "name": "couch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cpanel", "unicode": "&#xf388;", "name": "cpanel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commonscreative commons", "unicode": "&#xf25e;", "name": "creative-commons", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-bycreative commons attribution", "unicode": "&#xf4e7;", "name": "creative-commons-by", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-nccreative commons noncommercial", "unicode": "&#xf4e8;", "name": "creative-commons-nc", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-nc-eucreative commons noncommercial (euro sign)", "unicode": "&#xf4e9;", "name": "creative-commons-nc-eu", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-nc-jpcreative commons noncommercial (yen sign)", "unicode": "&#xf4ea;", "name": "creative-commons-nc-jp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-ndcreative commons no derivative works", "unicode": "&#xf4eb;", "name": "creative-commons-nd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-pdcreative commons public domain", "unicode": "&#xf4ec;", "name": "creative-commons-pd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-pd-altalternate creative commons public domain", "unicode": "&#xf4ed;", "name": "creative-commons-pd-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-remixcreative commons remix", "unicode": "&#xf4ee;", "name": "creative-commons-remix", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-sacreative commons share alike", "unicode": "&#xf4ef;", "name": "creative-commons-sa", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-samplingcreative commons sampling", "unicode": "&#xf4f0;", "name": "creative-commons-sampling", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-sampling-pluscreative commons sampling +", "unicode": "&#xf4f1;", "name": "creative-commons-sampling-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-sharecreative commons share", "unicode": "&#xf4f2;", "name": "creative-commons-share", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "creative-commons-zerocreative commons cc0", "unicode": "&#xf4f3;", "name": "creative-commons-zero", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "credit-card credit card buy checkout creditcardalt debit money payment purchase", "unicode": "&#xf09d;", "name": "credit-card", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "credit-card credit card buy checkout creditcardalt debit money payment purchase", "unicode": "&#xf09d;", "name": "credit-card", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "critical-role critical role dungeons  dragons dd dnd fantasy game gaming tabletop", "unicode": "&#xf6c9;", "name": "critical-role", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "crop design frame mask resize shrink", "unicode": "&#xf125;", "name": "crop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "crop-alt alternate crop design frame mask resize shrink", "unicode": "&#xf565;", "name": "crop-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cross catholicism christianity church jesus", "unicode": "&#xf654;", "name": "cross", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "crosshairs aim bullseye gpd picker position", "unicode": "&#xf05b;", "name": "crosshairs", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "crow bird bullfrog fauna halloween holiday toad", "unicode": "&#xf520;", "name": "crow", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "crown award favorite king queen royal tiara", "unicode": "&#xf521;", "name": "crown", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "crutch cane injury mobility wheelchair", "unicode": "&#xf7f7;", "name": "crutch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "css3 css 3 logo code", "unicode": "&#xf13c;", "name": "css3", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "css3-altalternate css3 logo", "unicode": "&#xf38b;", "name": "css3-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "cube 3d block dice package square tesseract", "unicode": "&#xf1b2;", "name": "cube", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cubes 3d block dice package pyramid square stack tesseract", "unicode": "&#xf1b3;", "name": "cubes", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cut clip scissors snip", "unicode": "&#xf0c4;", "name": "cut", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "cuttlefish", "unicode": "&#xf38c;", "name": "cuttlefish", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "d-and-ddungeons & dragons", "unicode": "&#xf38d;", "name": "d-and-d", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "d-and-d-beyond d&d beyond dungeons  dragons dd dnd fantasy gaming tabletop", "unicode": "&#xf6ca;", "name": "d-and-d-beyond", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dailymotion", "unicode": "&#xe052;", "name": "dailymotion", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dashcube", "unicode": "&#xf210;", "name": "dashcube", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "database computer development directory memory storage", "unicode": "&#xf1c0;", "name": "database", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "deaf ear hearing sign language", "unicode": "&#xf2a4;", "name": "deaf", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "<PERSON><PERSON>", "unicode": "&#xe077;", "name": "<PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "delicious", "unicode": "&#xf1a5;", "name": "delicious", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "democrat american democratic party donkey election left leftwing liberal politics usa", "unicode": "&#xf747;", "name": "democrat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "deploydogdeploy.dog", "unicode": "&#xf38e;", "name": "deploydog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "deskpro", "unicode": "&#xf38f;", "name": "deskpro", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "desktop computer cpu demo device imac machine monitor pc screen", "unicode": "&#xf108;", "name": "desktop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dev", "unicode": "&#xf6cc;", "name": "dev", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "deviantart", "unicode": "&#xf1bd;", "name": "deviantart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dharmachakra buddhism buddhist wheel of dharma", "unicode": "&#xf655;", "name": "dharma<PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dhl dalsey hillblom and lynn german package shipping", "unicode": "&#xf790;", "name": "dhl", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "diagnoses analyze detect diagnosis examine medicine", "unicode": "&#xf470;", "name": "diagnoses", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "diaspora", "unicode": "&#xf791;", "name": "diaspora", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dice chance gambling game roll", "unicode": "&#xf522;", "name": "dice", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-d20 dice d20 dungeons  dragons chance dd dnd fantasy gambling game roll", "unicode": "&#xf6cf;", "name": "dice-d20", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-d6 dice d6 dungeons  dragons chance dd dnd fantasy gambling game roll", "unicode": "&#xf6d1;", "name": "dice-d6", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-five dice five chance gambling game roll", "unicode": "&#xf523;", "name": "dice-five", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-four dice four chance gambling game roll", "unicode": "&#xf524;", "name": "dice-four", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-one dice one chance gambling game roll", "unicode": "&#xf525;", "name": "dice-one", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-six dice six chance gambling game roll", "unicode": "&#xf526;", "name": "dice-six", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-three dice three chance gambling game roll", "unicode": "&#xf527;", "name": "dice-three", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dice-two dice two chance gambling game roll", "unicode": "&#xf528;", "name": "dice-two", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "digg logo", "unicode": "&#xf1a6;", "name": "digg", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "digital-oceandigital ocean", "unicode": "&#xf391;", "name": "digital-ocean", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "digital-tachograph digital tachograph data distance speed tachometer", "unicode": "&#xf566;", "name": "digital-tachograph", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "directions map navigation sign turn", "unicode": "&#xf5eb;", "name": "directions", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "discord", "unicode": "&#xf392;", "name": "discord", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "discourse", "unicode": "&#xf393;", "name": "discourse", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "disease bacteria cancer covid19 illness infection sickness virus", "unicode": "&#xf7fa;", "name": "disease", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "divide arithmetic calculus division math", "unicode": "&#xf529;", "name": "divide", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dizzy face dazed dead disapprove emoticon face", "unicode": "&#xf567;", "name": "dizzy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dizzy face dazed dead disapprove emoticon face", "unicode": "&#xf567;", "name": "dizzy", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "dna double helix genetic helix molecule protein", "unicode": "&#xf471;", "name": "dna", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dochub", "unicode": "&#xf394;", "name": "dochub", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "docker", "unicode": "&#xf395;", "name": "docker", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dog animal canine fauna mammal pet pooch puppy woof", "unicode": "&#xf6d3;", "name": "dog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dollar-sign dollar sign  cost dollarsign money price usd", "unicode": "&#xf155;", "name": "dollar-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dolly carry shipping transport", "unicode": "&#xf472;", "name": "dolly", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dolly-flatbed dolly flatbed carry inventory shipping transport", "unicode": "&#xf474;", "name": "dolly-flatbed", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "donate contribute generosity gift give", "unicode": "&#xf4b9;", "name": "donate", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "door-closed door closed enter exit locked", "unicode": "&#xf52a;", "name": "door-closed", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "door-open door open enter exit welcome", "unicode": "&#xf52b;", "name": "door-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dot-circle dot circle bullseye notification target", "unicode": "&#xf192;", "name": "dot-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dot-circle dot circle bullseye notification target", "unicode": "&#xf192;", "name": "dot-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "dove bird fauna flying peace war", "unicode": "&#xf4ba;", "name": "dove", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "download export hard drive save transfer", "unicode": "&#xf019;", "name": "download", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "draft2digital", "unicode": "&#xf396;", "name": "draft2digital", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "drafting-compass drafting compass design map mechanical drawing plot plotting", "unicode": "&#xf568;", "name": "drafting-compass", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dragon dungeons  dragons dd dnd fantasy fire lizard serpent", "unicode": "&#xf6d5;", "name": "dragon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "draw-polygon draw polygon anchors lines object render shape", "unicode": "&#xf5ee;", "name": "draw-polygon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dribbble", "unicode": "&#xf17d;", "name": "dribbble", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dribbble-squaredribbble square", "unicode": "&#xf397;", "name": "dribbble-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dropbox", "unicode": "&#xf16b;", "name": "dropbox", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "drum instrument music percussion snare sound", "unicode": "&#xf569;", "name": "drum", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "drum-steelpan drum steelpan calypso instrument music percussion reggae snare sound steel tropical", "unicode": "&#xf56a;", "name": "drum-steelpan", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "drumstick-bite drumstick with bite taken out bone chicken leg meat poultry turkey", "unicode": "&#xf6d7;", "name": "drumstick-bite", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "drupal logo", "unicode": "&#xf1a9;", "name": "drupal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "dumbbell exercise gym strength weight weightlifting", "unicode": "&#xf44b;", "name": "dumbbell", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dumpster alley bin commercial trash waste", "unicode": "&#xf793;", "name": "dumpster", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dumpster-fire dumpster fire alley bin commercial danger dangerous euphemism flame heat hot trash waste", "unicode": "&#xf794;", "name": "dumpster-fire", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dungeons  dragons building dd dnd door entrance fantasy gate", "unicode": "&#xf6d9;", "name": "dungeon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "dyalog", "unicode": "&#xf399;", "name": "dyalog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "earlybirds", "unicode": "&#xf39a;", "name": "earlybirds", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ebay", "unicode": "&#xf4f4;", "name": "ebay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "edge browser browser ie", "unicode": "&#xf282;", "name": "edge", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "edge-legacyedge legacy browser", "unicode": "&#xe078;", "name": "edge-legacy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "edit pen pencil update write", "unicode": "&#xf044;", "name": "edit", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "edit pen pencil update write", "unicode": "&#xf044;", "name": "edit", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "egg breakfast chicken easter shell yolk", "unicode": "&#xf7fb;", "name": "egg", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "eject abort cancel cd discharge", "unicode": "&#xf052;", "name": "eject", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "elementor", "unicode": "&#xf430;", "name": "elementor", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ellipsis-h horizontal ellipsis dots drag kebab list menu nav navigation ol reorder settings ul", "unicode": "&#xf141;", "name": "ellipsis-h", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ellipsis-v vertical ellipsis dots drag kebab list menu nav navigation ol reorder settings ul", "unicode": "&#xf142;", "name": "ellipsis-v", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ello", "unicode": "&#xf5f1;", "name": "ello", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ember", "unicode": "&#xf423;", "name": "ember", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "galactic empire", "unicode": "&#xf1d1;", "name": "empire", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "envelope email email letter mail message notification support", "unicode": "&#xf0e0;", "name": "envelope", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "envelope email email letter mail message notification support", "unicode": "&#xf0e0;", "name": "envelope", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "envelope-open envelope open email email letter mail message notification support", "unicode": "&#xf2b6;", "name": "envelope-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "envelope-open envelope open email email letter mail message notification support", "unicode": "&#xf2b6;", "name": "envelope-open", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "envelope-open-text envelope open-text email email letter mail message notification support", "unicode": "&#xf658;", "name": "envelope-open-text", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "envelope-square envelope square email email letter mail message notification support", "unicode": "&#xf199;", "name": "envelope-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "envira gallery leaf", "unicode": "&#xf299;", "name": "envira", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "equals arithmetic even match math", "unicode": "&#xf52c;", "name": "equals", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "eraser art delete remove rubber", "unicode": "&#xf12d;", "name": "eraser", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "erlang", "unicode": "&#xf39d;", "name": "erlang", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ethereum", "unicode": "&#xf42e;", "name": "ethereum", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ethernet cable cat 5 cat 6 connection hardware internet network wired", "unicode": "&#xf796;", "name": "ethernet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "etsy", "unicode": "&#xf2d7;", "name": "etsy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "euro-sign euro sign currency dollar exchange money", "unicode": "&#xf153;", "name": "euro-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "evernote", "unicode": "&#xf839;", "name": "evernote", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "exchange-alt alternate exchange arrow arrows exchange reciprocate return swap transfer", "unicode": "&#xf362;", "name": "exchange-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "exclamation alert danger error important notice notification notify problem warning", "unicode": "&#xf12a;", "name": "exclamation", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "exclamation-circle exclamation circle alert danger error important notice notification notify problem warning", "unicode": "&#xf06a;", "name": "exclamation-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "exclamation-triangle exclamation triangle alert danger error important notice notification notify problem warning", "unicode": "&#xf071;", "name": "exclamation-triangle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "expand bigger enlarge fullscreen resize", "unicode": "&#xf065;", "name": "expand", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "expand-alt alternate expand arrows bigger enlarge fullscreen resize", "unicode": "&#xf424;", "name": "expand-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "expand-arrows-alt alternate expand arrows bigger enlarge fullscreen move resize", "unicode": "&#xf31e;", "name": "expand-arrows-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "expeditedssl", "unicode": "&#xf23e;", "name": "expeditedssl", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "external-link-alt alternate external link externallink new open share", "unicode": "&#xf35d;", "name": "external-link-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "external-link-square-alt alternate external link square externallinksquare new open share", "unicode": "&#xf360;", "name": "external-link-square-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "eye look optic see seen show sight views visible", "unicode": "&#xf06e;", "name": "eye", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "eye look optic see seen show sight views visible", "unicode": "&#xf06e;", "name": "eye", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "eye-dropper eye dropper beaker clone color copy eyedropper pipette", "unicode": "&#xf1fb;", "name": "eye-dropper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "eye-slash eye slash blind hide show toggle unseen views visible visiblity", "unicode": "&#xf070;", "name": "eye-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "eye-slash eye slash blind hide show toggle unseen views visible visiblity", "unicode": "&#xf070;", "name": "eye-slash", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "facebookofficial social network", "unicode": "&#xf09a;", "name": "facebook", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "facebook-f facebook f facebook", "unicode": "&#xf39e;", "name": "facebook-f", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "facebook-messengerfacebook messenger", "unicode": "&#xf39f;", "name": "facebook-messenger", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "facebook-square facebook square social network", "unicode": "&#xf082;", "name": "facebook-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fan ac air conditioning blade blower cool hot", "unicode": "&#xf863;", "name": "fan", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fantasy-flight-games fantasy flight-games dungeons  dragons dd dnd fantasy game gaming tabletop", "unicode": "&#xf6dc;", "name": "fantasy-flight-games", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fast-backward beginning first previous rewind start", "unicode": "&#xf049;", "name": "fast-backward", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fast-forward end last next", "unicode": "&#xf050;", "name": "fast-forward", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "faucet covid19 drip house hygiene kitchen sink water", "unicode": "&#xe005;", "name": "faucet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fax business communicate copy facsimile send", "unicode": "&#xf1ac;", "name": "fax", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "feather bird light plucked quill write", "unicode": "&#xf52d;", "name": "feather", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "feather-alt alternate feather bird light plucked quill write", "unicode": "&#xf56b;", "name": "feather-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fedex federal express package shipping", "unicode": "&#xf797;", "name": "fedex", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fedora linux operating system os", "unicode": "&#xf798;", "name": "fedora", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "female human person profile user woman", "unicode": "&#xf182;", "name": "female", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fighter-jet airplane fast fly goose maverick plane quick top gun transportation travel", "unicode": "&#xf0fb;", "name": "fighter-jet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "figma app design interface", "unicode": "&#xf799;", "name": "figma", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "file document new page pdf resume", "unicode": "&#xf15b;", "name": "file", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file document new page pdf resume", "unicode": "&#xf15b;", "name": "file", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-alt alternate file document filetext invoice new page pdf", "unicode": "&#xf15c;", "name": "file-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-alt alternate file document filetext invoice new page pdf", "unicode": "&#xf15c;", "name": "file-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-archive archive file zip bundle compress compression download zip", "unicode": "&#xf1c6;", "name": "file-archive", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-archive archive file zip bundle compress compression download zip", "unicode": "&#xf1c6;", "name": "file-archive", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-audio audio file document mp3 music page play sound", "unicode": "&#xf1c7;", "name": "file-audio", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-audio audio file document mp3 music page play sound", "unicode": "&#xf1c7;", "name": "file-audio", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-code code file css development document html", "unicode": "&#xf1c9;", "name": "file-code", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-code code file css development document html", "unicode": "&#xf1c9;", "name": "file-code", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-contract file contract agreement binding document legal signature", "unicode": "&#xf56c;", "name": "file-contract", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-csv file csv document excel numbers spreadsheets table", "unicode": "&#xf6dd;", "name": "file-csv", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-download file download document export save", "unicode": "&#xf56d;", "name": "file-download", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-excel excel file csv document numbers spreadsheets table", "unicode": "&#xf1c3;", "name": "file-excel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-excel excel file csv document numbers spreadsheets table", "unicode": "&#xf1c3;", "name": "file-excel", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-export file export download save", "unicode": "&#xf56e;", "name": "file-export", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-image image file document image jpg photo png", "unicode": "&#xf1c5;", "name": "file-image", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-image image file document image jpg photo png", "unicode": "&#xf1c5;", "name": "file-image", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-import file import copy document send upload", "unicode": "&#xf56f;", "name": "file-import", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-invoice file invoice account bill charge document payment receipt", "unicode": "&#xf570;", "name": "file-invoice", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-invoice-dollar file invoice with us dollar  account bill charge document dollarsign money payment receipt usd", "unicode": "&#xf571;", "name": "file-invoice-dollar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-medical medical file document health history prescription record", "unicode": "&#xf477;", "name": "file-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-medical-alt alternate medical file document health history prescription record", "unicode": "&#xf478;", "name": "file-medical-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-pdf pdf file acrobat document preview save", "unicode": "&#xf1c1;", "name": "file-pdf", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-pdf pdf file acrobat document preview save", "unicode": "&#xf1c1;", "name": "file-pdf", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-powerpoint powerpoint file display document keynote presentation", "unicode": "&#xf1c4;", "name": "file-powerpoint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-powerpoint powerpoint file display document keynote presentation", "unicode": "&#xf1c4;", "name": "file-powerpoint", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-prescription file prescription document drugs medical medicine rx", "unicode": "&#xf572;", "name": "file-prescription", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-signature file signature john ha<PERSON>ck contract document name", "unicode": "&#xf573;", "name": "file-signature", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-upload file upload document import page save", "unicode": "&#xf574;", "name": "file-upload", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-video video file document m4v movie mp4 play", "unicode": "&#xf1c8;", "name": "file-video", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-video video file document m4v movie mp4 play", "unicode": "&#xf1c8;", "name": "file-video", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "file-word word file document edit page text writing", "unicode": "&#xf1c2;", "name": "file-word", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "file-word word file document edit page text writing", "unicode": "&#xf1c2;", "name": "file-word", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "fill bucket color paint paint bucket", "unicode": "&#xf575;", "name": "fill", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fill-drip fill drip bucket color drop paint paint bucket spill", "unicode": "&#xf576;", "name": "fill-drip", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "film cinema movie strip video", "unicode": "&#xf008;", "name": "film", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "filter funnel options separate sort", "unicode": "&#xf0b0;", "name": "filter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fingerprint human id identification lock smudge touch unique unlock", "unicode": "&#xf577;", "name": "fingerprint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fire burn caliente flame heat hot popular", "unicode": "&#xf06d;", "name": "fire", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fire-alt alternate fire burn caliente flame heat hot popular", "unicode": "&#xf7e4;", "name": "fire-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fire-extinguisher burn caliente fire fighter flame heat hot rescue", "unicode": "&#xf134;", "name": "fire-extinguisher", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "firefox browser", "unicode": "&#xf269;", "name": "firefox", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "firefox-browser firefox browser browser", "unicode": "&#xe007;", "name": "firefox-browser", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "first-aid first aid emergency emt health medical rescue", "unicode": "&#xf479;", "name": "first-aid", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "first-orderfirst order", "unicode": "&#xf2b0;", "name": "first-order", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "first-order-altalternate first order", "unicode": "&#xf50a;", "name": "first-order-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "firstdraft", "unicode": "&#xf3a1;", "name": "firstdraft", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fish fauna gold seafood swimming", "unicode": "&#xf578;", "name": "fish", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fist-raised raised fist dungeons  dragons dd dnd fantasy hand ki monk resist strength unarmed combat", "unicode": "&#xf6de;", "name": "fist-raised", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "flag country notice notification notify pole report symbol", "unicode": "&#xf024;", "name": "flag", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "flag country notice notification notify pole report symbol", "unicode": "&#xf024;", "name": "flag", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "flag-checkered notice notification notify pole racing report symbol", "unicode": "&#xf11e;", "name": "flag-checkered", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "flag-usa united states of america flag betsy ross country old glory stars stripes symbol", "unicode": "&#xf74d;", "name": "flag-usa", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "flask beaker experimental labs science", "unicode": "&#xf0c3;", "name": "flask", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "flickr", "unicode": "&#xf16e;", "name": "flickr", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "flipboard", "unicode": "&#xf44d;", "name": "flipboard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "flushed face embarrassed emoticon face", "unicode": "&#xf579;", "name": "flushed", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "flushed face embarrassed emoticon face", "unicode": "&#xf579;", "name": "flushed", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "fly", "unicode": "&#xf417;", "name": "fly", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "folder archive directory document file", "unicode": "&#xf07b;", "name": "folder", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "folder archive directory document file", "unicode": "&#xf07b;", "name": "folder", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "folder-minus folder minus archive delete directory document file negative remove", "unicode": "&#xf65d;", "name": "folder-minus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "folder-open folder open archive directory document empty file new", "unicode": "&#xf07c;", "name": "folder-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "folder-open folder open archive directory document empty file new", "unicode": "&#xf07c;", "name": "folder-open", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "folder-plus folder plus add archive create directory document file new positive", "unicode": "&#xf65e;", "name": "folder-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "font alphabet glyph text type typeface", "unicode": "&#xf031;", "name": "font", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "font-awesome font awesome meanpath", "unicode": "&#xf2b4;", "name": "font-awesome", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "font-awesome-altalternate font awesome", "unicode": "&#xf35c;", "name": "font-awesome-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "font-awesome-flagfont awesome flag", "unicode": "&#xf425;", "name": "font-awesome-flag", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fonticons", "unicode": "&#xf280;", "name": "fonticons", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fonticons-fifonticons fi", "unicode": "&#xf3a2;", "name": "fonticons-fi", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "football-ball football ball ball fall nfl pigskin seasonal", "unicode": "&#xf44e;", "name": "football-ball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "fort-awesome fort awesome castle", "unicode": "&#xf286;", "name": "fort-awesome", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "fort-awesome-alt alternate fort awesome castle", "unicode": "&#xf3a3;", "name": "fort-awesome-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "forumbee", "unicode": "&#xf211;", "name": "forumbee", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "forward next skip", "unicode": "&#xf04e;", "name": "forward", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "foursquare", "unicode": "&#xf180;", "name": "foursquare", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "free-code-campfreecodecamp", "unicode": "&#xf2c5;", "name": "free-code-camp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "freebsd", "unicode": "&#xf3a4;", "name": "freebsd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "frog amphibian bullfrog fauna hop kermit kiss prince ribbit toad wart", "unicode": "&#xf52e;", "name": "frog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "frowning face disapprove emoticon face rating sad", "unicode": "&#xf119;", "name": "frown", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "frowning face disapprove emoticon face rating sad", "unicode": "&#xf119;", "name": "frown", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "frown-open frowning face with open mouth disapprove emoticon face rating sad", "unicode": "&#xf57a;", "name": "frown-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "frown-open frowning face with open mouth disapprove emoticon face rating sad", "unicode": "&#xf57a;", "name": "frown-open", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "fulcrum", "unicode": "&#xf50b;", "name": "fulcrum", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "funnel-dollar funnel dollar filter money options separate sort", "unicode": "&#xf662;", "name": "funnel-dollar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "futbol ball football mls soccer", "unicode": "&#xf1e3;", "name": "futbol", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "futbol ball football mls soccer", "unicode": "&#xf1e3;", "name": "futbol", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "galactic-republic galactic republic politics star wars", "unicode": "&#xf50c;", "name": "galactic-republic", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "galactic-senate galactic senate star wars", "unicode": "&#xf50d;", "name": "galactic-senate", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gamepad arcade controller dpad joystick video video game", "unicode": "&#xf11b;", "name": "gamepad", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gas-pump gas pump car fuel gasoline petrol", "unicode": "&#xf52f;", "name": "gas-pump", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gavel hammer judge law lawyer opinion", "unicode": "&#xf0e3;", "name": "gavel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gem diamond jewelry sapphire stone treasure", "unicode": "&#xf3a5;", "name": "gem", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gem diamond jewelry sapphire stone treasure", "unicode": "&#xf3a5;", "name": "gem", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "genderless androgynous asexual sexless", "unicode": "&#xf22d;", "name": "genderless", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "get-pocketget pocket", "unicode": "&#xf265;", "name": "get-pocket", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gg currency", "unicode": "&#xf260;", "name": "gg", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gg-circlegg currency circle", "unicode": "&#xf261;", "name": "gg-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ghost apparition blinky clyde floating halloween holiday inky pinky spirit", "unicode": "&#xf6e2;", "name": "ghost", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gift christmas generosity giving holiday party present wrapped xmas", "unicode": "&#xf06b;", "name": "gift", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gifts christmas generosity giving holiday party present wrapped xmas", "unicode": "&#xf79c;", "name": "gifts", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "git", "unicode": "&#xf1d3;", "name": "git", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "git-altgit alt", "unicode": "&#xf841;", "name": "git-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "git-squaregit square", "unicode": "&#xf1d2;", "name": "git-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "github octocat", "unicode": "&#xf09b;", "name": "github", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "github-alt alternate github octocat", "unicode": "&#xf113;", "name": "github-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "github-square github square octocat", "unicode": "&#xf092;", "name": "github-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gitkraken", "unicode": "&#xf3a6;", "name": "gitkraken", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gitlab axosoft", "unicode": "&#xf296;", "name": "gitlab", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gitter", "unicode": "&#xf426;", "name": "gitter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "glass-cheers glass cheers alcohol bar beverage celebration champagne clink drink holiday new years eve party toast", "unicode": "&#xf79f;", "name": "glass-cheers", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "glass-martini martini glass alcohol bar beverage drink liquor", "unicode": "&#xf000;", "name": "glass-martini", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "glass-martini-alt alternate glass martini alcohol bar beverage drink liquor", "unicode": "&#xf57b;", "name": "glass-martini-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "glass-whiskey glass whiskey alcohol bar beverage bourbon drink liquor neat rye scotch whisky", "unicode": "&#xf7a0;", "name": "glass-whiskey", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "glasses hipster nerd reading sight spectacles vision", "unicode": "&#xf530;", "name": "glasses", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "glide", "unicode": "&#xf2a5;", "name": "glide", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "glide-gglide g", "unicode": "&#xf2a6;", "name": "glide-g", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "globe all coordinates country earth global gps language localize location map online place planet translate travel world", "unicode": "&#xf0ac;", "name": "globe", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "globe-africa globe with africa shown all country earth global gps language localize location map online place planet translate travel world", "unicode": "&#xf57c;", "name": "globe-africa", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "globe-americas globe with americas shown all country earth global gps language localize location map online place planet translate travel world", "unicode": "&#xf57d;", "name": "globe-americas", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "globe-asia globe with asia shown all country earth global gps language localize location map online place planet translate travel world", "unicode": "&#xf57e;", "name": "globe-asia", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "globe-europe globe with europe shown all country earth global gps language localize location map online place planet translate travel world", "unicode": "&#xf7a2;", "name": "globe-europe", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gofore", "unicode": "&#xf3a7;", "name": "gofore", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "golf-ball golf ball caddy eagle putt tee", "unicode": "&#xf450;", "name": "golf-ball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "goodreads", "unicode": "&#xf3a8;", "name": "goodreads", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "goodreads-ggoodreads g", "unicode": "&#xf3a9;", "name": "goodreads-g", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google logo", "unicode": "&#xf1a0;", "name": "google", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-drivegoogle drive", "unicode": "&#xf3aa;", "name": "google-drive", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-paygoogle pay", "unicode": "&#xe079;", "name": "google-pay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-playgoogle play", "unicode": "&#xf3ab;", "name": "google-play", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-plus google plus googlepluscircle googleplusofficial", "unicode": "&#xf2b3;", "name": "google-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-plus-g google plus g googleplus social network", "unicode": "&#xf0d5;", "name": "google-plus-g", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-plus-square google plus square social network", "unicode": "&#xf0d4;", "name": "google-plus-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "google-walletgoogle wallet", "unicode": "&#xf1ee;", "name": "google-wallet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "gopuram building entrance hinduism temple tower", "unicode": "&#xf664;", "name": "gopuram", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "graduation-cap graduation cap ceremony college graduate learning school student", "unicode": "&#xf19d;", "name": "graduation-cap", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gratipay (gittip) favorite heart like love", "unicode": "&#xf184;", "name": "gratipay", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "grav", "unicode": "&#xf2d6;", "name": "grav", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "greater-than greater than arithmetic compare math", "unicode": "&#xf531;", "name": "greater-than", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "greater-than-equal greater than equal to arithmetic compare math", "unicode": "&#xf532;", "name": "greater-than-equal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grimace grimacing face cringe emoticon face teeth", "unicode": "&#xf57f;", "name": "grimace", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grimace grimacing face cringe emoticon face teeth", "unicode": "&#xf57f;", "name": "grimace", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grinning face emoticon face laugh smile", "unicode": "&#xf580;", "name": "grin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grinning face emoticon face laugh smile", "unicode": "&#xf580;", "name": "grin", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-alt alternate grinning face emoticon face laugh smile", "unicode": "&#xf581;", "name": "grin-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-alt alternate grinning face emoticon face laugh smile", "unicode": "&#xf581;", "name": "grin-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-beam grinning face with smiling eyes emoticon face laugh smile", "unicode": "&#xf582;", "name": "grin-beam", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-beam grinning face with smiling eyes emoticon face laugh smile", "unicode": "&#xf582;", "name": "grin-beam", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-beam-sweat grinning face with sweat embarass emoticon face smile", "unicode": "&#xf583;", "name": "grin-beam-sweat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-beam-sweat grinning face with sweat embarass emoticon face smile", "unicode": "&#xf583;", "name": "grin-beam-sweat", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-hearts smiling face with heart-eyes emoticon face love smile", "unicode": "&#xf584;", "name": "grin-hearts", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-hearts smiling face with heart-eyes emoticon face love smile", "unicode": "&#xf584;", "name": "grin-hearts", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-squint grinning squinting face emoticon face laugh smile", "unicode": "&#xf585;", "name": "grin-squint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-squint grinning squinting face emoticon face laugh smile", "unicode": "&#xf585;", "name": "grin-squint", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-squint-tears rolling on the floor laughing emoticon face happy smile", "unicode": "&#xf586;", "name": "grin-squint-tears", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-squint-tears rolling on the floor laughing emoticon face happy smile", "unicode": "&#xf586;", "name": "grin-squint-tears", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-stars star-struck emoticon face starstruck", "unicode": "&#xf587;", "name": "grin-stars", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-stars star-struck emoticon face starstruck", "unicode": "&#xf587;", "name": "grin-stars", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-tears face with tears of joy lol emoticon face", "unicode": "&#xf588;", "name": "grin-tears", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-tears face with tears of joy lol emoticon face", "unicode": "&#xf588;", "name": "grin-tears", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-tongue face with tongue lol emoticon face", "unicode": "&#xf589;", "name": "grin-tongue", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-tongue face with tongue lol emoticon face", "unicode": "&#xf589;", "name": "grin-tongue", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-tongue-squint squinting face with tongue lol emoticon face", "unicode": "&#xf58a;", "name": "grin-tongue-squint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-tongue-squint squinting face with tongue lol emoticon face", "unicode": "&#xf58a;", "name": "grin-tongue-squint", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-tongue-wink winking face with tongue lol emoticon face", "unicode": "&#xf58b;", "name": "grin-tongue-wink", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-tongue-wink winking face with tongue lol emoticon face", "unicode": "&#xf58b;", "name": "grin-tongue-wink", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grin-wink grinning winking face emoticon face flirt laugh smile", "unicode": "&#xf58c;", "name": "grin-wink", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grin-wink grinning winking face emoticon face flirt laugh smile", "unicode": "&#xf58c;", "name": "grin-wink", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "grip-horizontal grip horizontal affordance drag drop grab handle", "unicode": "&#xf58d;", "name": "grip-horizontal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grip-lines grip lines affordance drag drop grab handle", "unicode": "&#xf7a4;", "name": "grip-lines", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grip-lines-vertical grip lines vertical affordance drag drop grab handle", "unicode": "&#xf7a5;", "name": "grip-lines-vertical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grip-vertical grip vertical affordance drag drop grab handle", "unicode": "&#xf58e;", "name": "grip-vertical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gripfire, inc.", "unicode": "&#xf3ac;", "name": "gripfire", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "grunt", "unicode": "&#xf3ad;", "name": "grunt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "guilded", "unicode": "&#xe07e;", "name": "guilded", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "guitar acoustic instrument music rock rock and roll song strings", "unicode": "&#xf7a6;", "name": "guitar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "gulp", "unicode": "&#xf3ae;", "name": "gulp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "h-square h square directions emergency hospital hotel map", "unicode": "&#xf0fd;", "name": "h-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hacker-newshacker news", "unicode": "&#xf1d4;", "name": "hacker-news", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hacker-news-squarehacker news square", "unicode": "&#xf3af;", "name": "hacker-news-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hackerrank", "unicode": "&#xf5f7;", "name": "hackerrank", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hamburger bacon beef burger burger king cheeseburger fast food grill ground beef mcdonalds sandwich", "unicode": "&#xf805;", "name": "hamburger", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hammer admin fix repair settings tool", "unicode": "&#xf6e3;", "name": "hammer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hamsa amulet christianity islam jewish judaism muslim protection", "unicode": "&#xf665;", "name": "hamsa", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-holding hand holding carry lift", "unicode": "&#xf4bd;", "name": "hand-holding", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-holding-heart hand holding heart carry charity gift lift package", "unicode": "&#xf4be;", "name": "hand-holding-heart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-holding-medical hand holding medical cross care covid19 donate help", "unicode": "&#xe05c;", "name": "hand-holding-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-holding-usd hand holding us dollar  carry dollar sign donation giving lift money price", "unicode": "&#xf4c0;", "name": "hand-holding-usd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-holding-water hand holding water carry covid19 drought grow lift", "unicode": "&#xf4c1;", "name": "hand-holding-water", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-lizard lizard (hand) game roshambo", "unicode": "&#xf258;", "name": "hand-lizard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-lizard lizard (hand) game roshambo", "unicode": "&#xf258;", "name": "hand-lizard", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-middle-finger hand with middle finger raised flip the bird gesture hate rude", "unicode": "&#xf806;", "name": "hand-middle-finger", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-paper paper (hand) game halt roshambo stop", "unicode": "&#xf256;", "name": "hand-paper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-paper paper (hand) game halt roshambo stop", "unicode": "&#xf256;", "name": "hand-paper", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-peace peace (hand) rest truce", "unicode": "&#xf25b;", "name": "hand-peace", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-peace peace (hand) rest truce", "unicode": "&#xf25b;", "name": "hand-peace", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-point-down hand pointing down finger handodown point", "unicode": "&#xf0a7;", "name": "hand-point-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-point-down hand pointing down finger handodown point", "unicode": "&#xf0a7;", "name": "hand-point-down", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-point-left hand pointing left back finger handoleft left point previous", "unicode": "&#xf0a5;", "name": "hand-point-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-point-left hand pointing left back finger handoleft left point previous", "unicode": "&#xf0a5;", "name": "hand-point-left", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-point-right hand pointing right finger forward handoright next point right", "unicode": "&#xf0a4;", "name": "hand-point-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-point-right hand pointing right finger forward handoright next point right", "unicode": "&#xf0a4;", "name": "hand-point-right", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-point-up hand pointing up finger handoup point", "unicode": "&#xf0a6;", "name": "hand-point-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-point-up hand pointing up finger handoup point", "unicode": "&#xf0a6;", "name": "hand-point-up", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-pointer pointer (hand) arrow cursor select", "unicode": "&#xf25a;", "name": "hand-pointer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-pointer pointer (hand) arrow cursor select", "unicode": "&#xf25a;", "name": "hand-pointer", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-rock rock (hand) fist game roshambo", "unicode": "&#xf255;", "name": "hand-rock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-rock rock (hand) fist game roshambo", "unicode": "&#xf255;", "name": "hand-rock", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-scissors scissors (hand) cut game roshambo", "unicode": "&#xf257;", "name": "hand-scissors", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-scissors scissors (hand) cut game roshambo", "unicode": "&#xf257;", "name": "hand-scissors", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hand-sparkles hand sparkles clean covid19 hygiene magic soap wash", "unicode": "&#xe05d;", "name": "hand-sparkles", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-spock spock (hand) live long prosper salute star trek vulcan", "unicode": "&#xf259;", "name": "hand-spock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hand-spock spock (hand) live long prosper salute star trek vulcan", "unicode": "&#xf259;", "name": "hand-spock", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hands carry hold lift", "unicode": "&#xf4c2;", "name": "hands", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hands-helping helping hands aid assistance handshake partnership volunteering", "unicode": "&#xf4c4;", "name": "hands-helping", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hands-wash hands wash covid19 hygiene soap wash", "unicode": "&#xe05e;", "name": "hands-wash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "handshake agreement greeting meeting partnership", "unicode": "&#xf2b5;", "name": "handshake", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "handshake agreement greeting meeting partnership", "unicode": "&#xf2b5;", "name": "handshake", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "handshake-alt-slash handshake alternate slash broken covid19 social distance", "unicode": "&#xe05f;", "name": "handshake-alt-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "handshake-slash handshake slash broken covid19 social distance", "unicode": "&#xe060;", "name": "handshake-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hanukiah candle hanukkah jewish judaism light", "unicode": "&#xf6e6;", "name": "hanu<PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hard-hat hard hat construction hardhat helmet safety", "unicode": "&#xf807;", "name": "hard-hat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hashtag twitter instagram pound social media tag", "unicode": "&#xf292;", "name": "hashtag", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hat-cowboy cowboy hat buckaroo horse jackeroo john b old west pardner ranch rancher rodeo western wrangler", "unicode": "&#xf8c0;", "name": "hat-cowboy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hat-cowboy-side cowboy hat side buckaroo horse jackeroo john b old west pardner ranch rancher rodeo western wrangler", "unicode": "&#xf8c1;", "name": "hat-cowboy-side", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hat-wizard wizard's hat dungeons  dragons accessory buckle clothing dd dnd fantasy halloween head holiday mage magic pointy witch", "unicode": "&#xf6e8;", "name": "hat-wizard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hdd cpu hard drive harddrive machine save storage", "unicode": "&#xf0a0;", "name": "hdd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hdd cpu hard drive harddrive machine save storage", "unicode": "&#xf0a0;", "name": "hdd", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "head-side-cough head side cough cough covid19 germs lungs respiratory sick", "unicode": "&#xe061;", "name": "head-side-cough", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "head-side-cough-slash head side-cough-slash cough covid19 germs lungs respiratory sick", "unicode": "&#xe062;", "name": "head-side-cough-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "head-side-mask head side mask breath covid19 filter respirator virus", "unicode": "&#xe063;", "name": "head-side-mask", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "head-side-virus head side virus cold covid19 flu sick", "unicode": "&#xe064;", "name": "head-side-virus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "heading format header text title", "unicode": "&#xf1dc;", "name": "heading", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "headphones audio listen music sound speaker", "unicode": "&#xf025;", "name": "headphones", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "headphones-alt alternate headphones audio listen music sound speaker", "unicode": "&#xf58f;", "name": "headphones-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "headset audio gamer gaming listen live chat microphone shot caller sound support telemarketer", "unicode": "&#xf590;", "name": "headset", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "heart favorite like love relationship valentine", "unicode": "&#xf004;", "name": "heart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "heart favorite like love relationship valentine", "unicode": "&#xf004;", "name": "heart", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "heart-broken heart broken breakup crushed dislike dumped grief love lovesick relationship sad", "unicode": "&#xf7a9;", "name": "heart-broken", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "heartbeat ekg electrocardiogram health lifeline vital signs", "unicode": "&#xf21e;", "name": "heartbeat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "helicopter airwolf apache chopper flight fly travel", "unicode": "&#xf533;", "name": "helicopter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "highlighter edit marker sharpie update write", "unicode": "&#xf591;", "name": "highlighter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hiking activity backpack fall fitness outdoors person seasonal walking", "unicode": "&#xf6ec;", "name": "hiking", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hippo animal fauna hippopotamus hungry mammal", "unicode": "&#xf6ed;", "name": "hippo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hips", "unicode": "&#xf452;", "name": "hips", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hire-a-helper<PERSON><PERSON><PERSON><PERSON>", "unicode": "&#xf3b0;", "name": "hire-a-helper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "history rewind clock reverse time time machine", "unicode": "&#xf1da;", "name": "history", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hive blockchain network", "unicode": "&#xe07f;", "name": "hive", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hockey-puck hockey puck ice nhl sport", "unicode": "&#xf453;", "name": "hockey-puck", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "holly-berry holly berry catwoman christmas decoration flora halle holiday ororo munroe plant storm xmas", "unicode": "&#xf7aa;", "name": "holly-berry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "home abode building house main", "unicode": "&#xf015;", "name": "home", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hooli", "unicode": "&#xf427;", "name": "hooli", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hornbill", "unicode": "&#xf592;", "name": "hornbill", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "horse equus fauna mammmal mare neigh pony", "unicode": "&#xf6f0;", "name": "horse", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "horse-head horse head equus fauna mammmal mare neigh pony", "unicode": "&#xf7ab;", "name": "horse-head", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hospital building covid19 emergency room medical center", "unicode": "&#xf0f8;", "name": "hospital", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hospital building covid19 emergency room medical center", "unicode": "&#xf0f8;", "name": "hospital", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hospital-alt alternate hospital building covid19 emergency room medical center", "unicode": "&#xf47d;", "name": "hospital-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hospital-symbol hospital symbol clinic covid19 emergency map", "unicode": "&#xf47e;", "name": "hospital-symbol", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hospital-user hospital with user covid19 doctor network patient primary care", "unicode": "&#xf80d;", "name": "hospital-user", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hot-tub hot tub bath jacuzzi massage sauna spa", "unicode": "&#xf593;", "name": "hot-tub", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hotdog hot dog bun chili frankfurt frankfurter kosher polish sandwich sausage vienna weiner", "unicode": "&#xf80f;", "name": "hotdog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hotel building inn lodging motel resort travel", "unicode": "&#xf594;", "name": "hotel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hotjar", "unicode": "&#xf3b1;", "name": "hotjar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hourglass hour minute sand stopwatch time", "unicode": "&#xf254;", "name": "hourglass", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hourglass hour minute sand stopwatch time", "unicode": "&#xf254;", "name": "hourglass", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "hourglass-end hourglass end hour minute sand stopwatch time", "unicode": "&#xf253;", "name": "hourglass-end", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hourglass-half hourglass half hour minute sand stopwatch time", "unicode": "&#xf252;", "name": "hourglass-half", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "hourglass-start hourglass start hour minute sand stopwatch time", "unicode": "&#xf251;", "name": "hourglass-start", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "house-damage damaged house building devastation disaster home insurance", "unicode": "&#xf6f1;", "name": "house-damage", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "house-user house user covid19 home isolation quarantine", "unicode": "&#xe065;", "name": "house-user", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "<PERSON>uzz", "unicode": "&#xf27c;", "name": "<PERSON>uzz", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hryvnia currency money ukraine ukrainian", "unicode": "&#xf6f2;", "name": "hryvnia", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "html5html 5 logo", "unicode": "&#xf13b;", "name": "html5", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "hubspot", "unicode": "&#xf3b2;", "name": "hubspot", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "i-cursor i beam cursor editing ibeam type writing", "unicode": "&#xf246;", "name": "i-cursor", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ice-cream ice cream chocolate cone dessert frozen scoop sorbet vanilla yogurt", "unicode": "&#xf810;", "name": "ice-cream", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "icicles cold frozen hanging ice seasonal sharp", "unicode": "&#xf7ad;", "name": "icicles", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "icons bolt emoji heart image music photo symbols", "unicode": "&#xf86d;", "name": "icons", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "id-badge identification badge address contact identification license profile", "unicode": "&#xf2c1;", "name": "id-badge", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "id-badge identification badge address contact identification license profile", "unicode": "&#xf2c1;", "name": "id-badge", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "id-card identification card contact demographics document identification issued profile", "unicode": "&#xf2c2;", "name": "id-card", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "id-card identification card contact demographics document identification issued profile", "unicode": "&#xf2c2;", "name": "id-card", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "id-card-alt alternate identification card contact demographics document identification issued profile", "unicode": "&#xf47f;", "name": "id-card-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ideal", "unicode": "&#xe013;", "name": "ideal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "igloo dome dwelling eskimo home house ice snow", "unicode": "&#xf7ae;", "name": "igloo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "image album landscape photo picture", "unicode": "&#xf03e;", "name": "image", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "image album landscape photo picture", "unicode": "&#xf03e;", "name": "image", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "images album landscape photo picture", "unicode": "&#xf302;", "name": "images", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "images album landscape photo picture", "unicode": "&#xf302;", "name": "images", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "imdb", "unicode": "&#xf2d8;", "name": "imdb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "inbox archive desk email mail message", "unicode": "&#xf01c;", "name": "inbox", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "indent align justify paragraph tab", "unicode": "&#xf03c;", "name": "indent", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "industry building factory industrial manufacturing mill warehouse", "unicode": "&#xf275;", "name": "industry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "infinity eternity forever math", "unicode": "&#xf534;", "name": "infinity", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "info details help information more support", "unicode": "&#xf129;", "name": "info", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "info-circle info circle details help information more support", "unicode": "&#xf05a;", "name": "info-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "innosoft", "unicode": "&#xe080;", "name": "innosoft", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "instagram", "unicode": "&#xf16d;", "name": "instagram", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "instagram-squareinstagram square", "unicode": "&#xe055;", "name": "instagram-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "instalod", "unicode": "&#xe081;", "name": "instalod", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "intercom app customer messenger", "unicode": "&#xf7af;", "name": "intercom", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "internet-explorer browser ie", "unicode": "&#xf26b;", "name": "internet-explorer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "invision app design interface", "unicode": "&#xf7b0;", "name": "invision", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ioxhost", "unicode": "&#xf208;", "name": "ioxhost", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "italic edit emphasis font format text type", "unicode": "&#xf033;", "name": "italic", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "itch-ioitch.io", "unicode": "&#xf83a;", "name": "itch-io", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "itunes", "unicode": "&#xf3b4;", "name": "itunes", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "itunes-noteitunes note", "unicode": "&#xf3b5;", "name": "itunes-note", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "java", "unicode": "&#xf4e4;", "name": "java", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "jedi crest force sith skywalker star wars yoda", "unicode": "&#xf669;", "name": "jedi", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "jedi-order jedi order star wars", "unicode": "&#xf50e;", "name": "jedi-order", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unicode": "&#xf3b6;", "name": "jenkins", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "<PERSON>ra at<PERSON>ian", "unicode": "&#xf7b1;", "name": "jira", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "joget", "unicode": "&#xf3b7;", "name": "joget", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "joint blunt cannabis doobie drugs marijuana roach smoke smoking spliff", "unicode": "&#xf595;", "name": "joint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "joomla logo", "unicode": "&#xf1aa;", "name": "j<PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "journal-whills journal of the whills book force jedi sith star wars yoda", "unicode": "&#xf66a;", "name": "journal-whills", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "javascript (js)", "unicode": "&#xf3b8;", "name": "js", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "js-squarejavascript (js) square", "unicode": "&#xf3b9;", "name": "js-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "jsfiddle", "unicode": "&#xf1cc;", "name": "jsfiddle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "kaaba building cube islam muslim", "unicode": "&#xf66b;", "name": "kaaba", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "kaggle", "unicode": "&#xf5fa;", "name": "kaggle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "key lock password private secret unlock", "unicode": "&#xf084;", "name": "key", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "keybase", "unicode": "&#xf4f5;", "name": "keybase", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "keyboard accessory edit input text type write", "unicode": "&#xf11c;", "name": "keyboard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "keyboard accessory edit input text type write", "unicode": "&#xf11c;", "name": "keyboard", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "keycdn", "unicode": "&#xf3ba;", "name": "keycdn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "khanda chakkar sikh sikhism sword", "unicode": "&#xf66d;", "name": "khanda", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "kickstarter", "unicode": "&#xf3bb;", "name": "kickstarter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "kickstarter-kkickstarter k", "unicode": "&#xf3bc;", "name": "kickstarter-k", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "kissing face beso emoticon face love smooch", "unicode": "&#xf596;", "name": "kiss", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "kissing face beso emoticon face love smooch", "unicode": "&#xf596;", "name": "kiss", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "kiss-beam kissing face with smiling eyes beso emoticon face love smooch", "unicode": "&#xf597;", "name": "kiss-beam", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "kiss-beam kissing face with smiling eyes beso emoticon face love smooch", "unicode": "&#xf597;", "name": "kiss-beam", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "kiss-wink-heart face blowing a kiss beso emoticon face love smooch", "unicode": "&#xf598;", "name": "kiss-wink-heart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "kiss-wink-heart face blowing a kiss beso emoticon face love smooch", "unicode": "&#xf598;", "name": "kiss-wink-heart", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "kiwi-bird kiwi bird bird fauna new zealand", "unicode": "&#xf535;", "name": "kiwi-bird", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "korvue", "unicode": "&#xf42f;", "name": "korvue", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "landmark building historic memorable monument politics", "unicode": "&#xf66f;", "name": "landmark", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "language dialect idiom localize speech translate vernacular", "unicode": "&#xf1ab;", "name": "language", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laptop computer cpu dell demo device mac macbook machine pc", "unicode": "&#xf109;", "name": "laptop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laptop-code laptop code computer cpu dell demo develop device mac macbook machine pc", "unicode": "&#xf5fc;", "name": "laptop-code", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laptop-house laptop house computer covid19 device office remote work from home", "unicode": "&#xe066;", "name": "laptop-house", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laptop-medical laptop medical computer device ehr electronic health records history", "unicode": "&#xf812;", "name": "laptop-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laravel", "unicode": "&#xf3bd;", "name": "laravel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "lastfmlast.fm", "unicode": "&#xf202;", "name": "lastfm", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "lastfm-squarelast.fm square", "unicode": "&#xf203;", "name": "lastfm-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "grinning face with big eyes lol emoticon face laugh smile", "unicode": "&#xf599;", "name": "laugh", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "grinning face with big eyes lol emoticon face laugh smile", "unicode": "&#xf599;", "name": "laugh", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "laugh-beam laugh face with beaming eyes lol emoticon face happy smile", "unicode": "&#xf59a;", "name": "laugh-beam", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laugh-beam laugh face with beaming eyes lol emoticon face happy smile", "unicode": "&#xf59a;", "name": "laugh-beam", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "laugh-squint laughing squinting face lol emoticon face happy smile", "unicode": "&#xf59b;", "name": "laugh-squint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laugh-squint laughing squinting face lol emoticon face happy smile", "unicode": "&#xf59b;", "name": "laugh-squint", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "laugh-wink laughing winking face lol emoticon face happy smile", "unicode": "&#xf59c;", "name": "laugh-wink", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "laugh-wink laughing winking face lol emoticon face happy smile", "unicode": "&#xf59c;", "name": "laugh-wink", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "layer-group layer group arrange develop layers map stack", "unicode": "&#xf5fd;", "name": "layer-group", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "leaf eco flora nature plant vegan", "unicode": "&#xf06c;", "name": "leaf", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "leanpub", "unicode": "&#xf212;", "name": "leanpub", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "lemon citrus lemonade lime tart", "unicode": "&#xf094;", "name": "lemon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lemon citrus lemonade lime tart", "unicode": "&#xf094;", "name": "lemon", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "less", "unicode": "&#xf41d;", "name": "less", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "less-than less than arithmetic compare math", "unicode": "&#xf536;", "name": "less-than", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "less-than-equal less than equal to arithmetic compare math", "unicode": "&#xf537;", "name": "less-than-equal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "level-down-alt alternate level down arrow leveldown", "unicode": "&#xf3be;", "name": "level-down-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "level-up-alt alternate level up arrow levelup", "unicode": "&#xf3bf;", "name": "level-up-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "life-ring life ring coast guard help overboard save support", "unicode": "&#xf1cd;", "name": "life-ring", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "life-ring life ring coast guard help overboard save support", "unicode": "&#xf1cd;", "name": "life-ring", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "lightbulb energy idea inspiration light", "unicode": "&#xf0eb;", "name": "lightbulb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lightbulb energy idea inspiration light", "unicode": "&#xf0eb;", "name": "lightbulb", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "line", "unicode": "&#xf3c0;", "name": "line", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "link attach attachment chain connect", "unicode": "&#xf0c1;", "name": "link", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "linkedinsquare", "unicode": "&#xf08c;", "name": "linkedin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "linkedin-in linkedin in linkedin", "unicode": "&#xf0e1;", "name": "linkedin-in", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "linode", "unicode": "&#xf2b8;", "name": "linode", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "linux tux", "unicode": "&#xf17c;", "name": "linux", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "lira-sign turkish lira sign currency money try turkish", "unicode": "&#xf195;", "name": "lira-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "list checklist completed done finished ol todo ul", "unicode": "&#xf03a;", "name": "list", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "list-alt alternate list checklist completed done finished ol todo ul", "unicode": "&#xf022;", "name": "list-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "list-alt alternate list checklist completed done finished ol todo ul", "unicode": "&#xf022;", "name": "list-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "list-ol checklist completed done finished numbers ol todo ul", "unicode": "&#xf0cb;", "name": "list-ol", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "list-ul checklist completed done finished ol todo ul", "unicode": "&#xf0ca;", "name": "list-ul", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "location-arrow address compass coordinate direction gps map navigation place", "unicode": "&#xf124;", "name": "location-arrow", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lock admin open password private protect security", "unicode": "&#xf023;", "name": "lock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lock-open lock open admin lock open password private protect security", "unicode": "&#xf3c1;", "name": "lock-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "long-arrow-alt-down alternate long arrow down download longarrowdown", "unicode": "&#xf309;", "name": "long-arrow-alt-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "long-arrow-alt-left alternate long arrow left back longarrowleft previous", "unicode": "&#xf30a;", "name": "long-arrow-alt-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "long-arrow-alt-right alternate long arrow right forward long<PERSON><PERSON><PERSON> next", "unicode": "&#xf30b;", "name": "long-arrow-alt-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "long-arrow-alt-up alternate long arrow up longarrowup upload", "unicode": "&#xf30c;", "name": "long-arrow-alt-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "low-vision low vision blind eye sight", "unicode": "&#xf2a8;", "name": "low-vision", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "luggage-cart luggage cart bag baggage suitcase travel", "unicode": "&#xf59d;", "name": "luggage-cart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lungs air breath covid19 organ respiratory", "unicode": "&#xf604;", "name": "lungs", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lungs-virus lungs virus breath covid19 respiratory sick", "unicode": "&#xe067;", "name": "lungs-virus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "lyft", "unicode": "&#xf3c3;", "name": "lyft", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "magento", "unicode": "&#xf3c4;", "name": "magento", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "magic autocomplete automatic mage spell wand witch wizard", "unicode": "&#xf0d0;", "name": "magic", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "magnet attract lodestone tool", "unicode": "&#xf076;", "name": "magnet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mail-bulk mail bulk archive envelope letter post office postal postcard send stamp usps", "unicode": "&#xf674;", "name": "mail-bulk", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mailchimp", "unicode": "&#xf59e;", "name": "mailchimp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "male human man person profile user", "unicode": "&#xf183;", "name": "male", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mandalorian", "unicode": "&#xf50f;", "name": "mandalorian", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "map address coordinates destination gps localize location navigation paper pin place point of interest position route travel", "unicode": "&#xf279;", "name": "map", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "map address coordinates destination gps localize location navigation paper pin place point of interest position route travel", "unicode": "&#xf279;", "name": "map", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "map-marked map marked address coordinates destination gps localize location map navigation paper pin place point of interest position route travel", "unicode": "&#xf59f;", "name": "map-marked", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "map-marked-alt alternate map marked address coordinates destination gps localize location map navigation paper pin place point of interest position route travel", "unicode": "&#xf5a0;", "name": "map-marked-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "map-marker address coordinates destination gps localize location map navigation paper pin place point of interest position route travel", "unicode": "&#xf041;", "name": "map-marker", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "map-marker-alt alternate map marker address coordinates destination gps localize location map navigation paper pin place point of interest position route travel", "unicode": "&#xf3c5;", "name": "map-marker-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "map-pin map pin address agree coordinates destination gps localize location map marker navigation pin place position travel", "unicode": "&#xf276;", "name": "map-pin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "map-signs map signs directions directory map signage wayfinding", "unicode": "&#xf277;", "name": "map-signs", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "markdown", "unicode": "&#xf60f;", "name": "markdown", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "marker design edit sharpie update write", "unicode": "&#xf5a1;", "name": "marker", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mars male", "unicode": "&#xf222;", "name": "mars", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mars-doublemars double", "unicode": "&#xf227;", "name": "mars-double", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mars-strokemars stroke", "unicode": "&#xf229;", "name": "mars-stroke", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mars-stroke-hmars stroke horizontal", "unicode": "&#xf22b;", "name": "mars-stroke-h", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mars-stroke-vmars stroke vertical", "unicode": "&#xf22a;", "name": "mars-stroke-v", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mask carnivale costume disguise halloween secret super hero", "unicode": "&#xf6fa;", "name": "mask", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mastodon", "unicode": "&#xf4f6;", "name": "mastodon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "maxcdn", "unicode": "&#xf136;", "name": "maxcdn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "mdbmaterial design for bootstrap", "unicode": "&#xf8ca;", "name": "mdb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "medal award ribbon star trophy", "unicode": "&#xf5a2;", "name": "medal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "medapps", "unicode": "&#xf3c6;", "name": "medapps", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "medium", "unicode": "&#xf23a;", "name": "medium", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "medium-mmedium m", "unicode": "&#xf3c7;", "name": "medium-m", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "medkit first aid firstaid health help support", "unicode": "&#xf0fa;", "name": "medkit", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "medrtmrt", "unicode": "&#xf3c8;", "name": "medrt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "meetup", "unicode": "&#xf2e0;", "name": "meetup", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "megaport", "unicode": "&#xf5a3;", "name": "megaport", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "meh neutral face emoticon face neutral rating", "unicode": "&#xf11a;", "name": "meh", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "meh neutral face emoticon face neutral rating", "unicode": "&#xf11a;", "name": "meh", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "meh-blank face without mouth emoticon face neutral rating", "unicode": "&#xf5a4;", "name": "meh-blank", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "meh-blank face without mouth emoticon face neutral rating", "unicode": "&#xf5a4;", "name": "meh-blank", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "meh-rolling-eyes face with rolling eyes emoticon face neutral rating", "unicode": "&#xf5a5;", "name": "meh-rolling-eyes", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "meh-rolling-eyes face with rolling eyes emoticon face neutral rating", "unicode": "&#xf5a5;", "name": "meh-rolling-eyes", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "memory dimm ram hardware storage technology", "unicode": "&#xf538;", "name": "memory", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mendeley", "unicode": "&#xf7b3;", "name": "mendeley", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "menorah candle hanukkah jewish judaism light", "unicode": "&#xf676;", "name": "menorah", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mercury transgender", "unicode": "&#xf223;", "name": "mercury", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "meteor armageddon asteroid comet shooting star space", "unicode": "&#xf753;", "name": "meteor", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microblogmicro.blog", "unicode": "&#xe01a;", "name": "microblog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "microchip cpu hardware processor technology", "unicode": "&#xf2db;", "name": "microchip", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microphone audio podcast record sing sound voice", "unicode": "&#xf130;", "name": "microphone", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microphone-alt alternate microphone audio podcast record sing sound voice", "unicode": "&#xf3c9;", "name": "microphone-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microphone-alt-slash alternate microphone slash audio disable mute podcast record sing sound voice", "unicode": "&#xf539;", "name": "microphone-alt-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microphone-slash microphone slash audio disable mute podcast record sing sound voice", "unicode": "&#xf131;", "name": "microphone-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microscope covid19 electron lens optics science shrink", "unicode": "&#xf610;", "name": "microscope", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "microsoft", "unicode": "&#xf3ca;", "name": "microsoft", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "minus collapse delete hide minify negative remove trash", "unicode": "&#xf068;", "name": "minus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "minus-circle minus circle delete hide negative remove shape trash", "unicode": "&#xf056;", "name": "minus-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "minus-square minus square collapse delete hide minify negative remove shape trash", "unicode": "&#xf146;", "name": "minus-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "minus-square minus square collapse delete hide minify negative remove shape trash", "unicode": "&#xf146;", "name": "minus-square", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "mitten clothing cold glove hands knitted seasonal warmth", "unicode": "&#xf7b5;", "name": "mitten", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mix", "unicode": "&#xf3cb;", "name": "mix", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "mixcloud", "unicode": "&#xf289;", "name": "mixcloud", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "mixer", "unicode": "&#xe056;", "name": "mixer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "<PERSON><PERSON>ni", "unicode": "&#xf3cc;", "name": "<PERSON><PERSON>ni", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "mobile phone apple call cell phone cellphone device iphone number screen telephone", "unicode": "&#xf10b;", "name": "mobile", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mobile-alt alternate mobile apple call cell phone cellphone device iphone number screen telephone", "unicode": "&#xf3cd;", "name": "mobile-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "modx", "unicode": "&#xf285;", "name": "modx", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "monero", "unicode": "&#xf3d0;", "name": "monero", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "money-bill money bill buy cash checkout money payment price purchase", "unicode": "&#xf0d6;", "name": "money-bill", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "money-bill-alt alternate money bill buy cash checkout money payment price purchase", "unicode": "&#xf3d1;", "name": "money-bill-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "money-bill-alt alternate money bill buy cash checkout money payment price purchase", "unicode": "&#xf3d1;", "name": "money-bill-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "money-bill-wave wavy money bill buy cash checkout money payment price purchase", "unicode": "&#xf53a;", "name": "money-bill-wave", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "money-bill-wave-alt alternate wavy money bill buy cash checkout money payment price purchase", "unicode": "&#xf53b;", "name": "money-bill-wave-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "money-check money check bank check buy checkout cheque money payment price purchase", "unicode": "&#xf53c;", "name": "money-check", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "money-check-alt alternate money check bank check buy checkout cheque money payment price purchase", "unicode": "&#xf53d;", "name": "money-check-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "monument building historic landmark memorable", "unicode": "&#xf5a6;", "name": "monument", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "moon contrast crescent dark lunar night", "unicode": "&#xf186;", "name": "moon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "moon contrast crescent dark lunar night", "unicode": "&#xf186;", "name": "moon", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "mortar-pestle mortar pestle crush culinary grind medical mix pharmacy prescription spices", "unicode": "&#xf5a7;", "name": "mortar-pestle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mosque building islam landmark muslim", "unicode": "&#xf678;", "name": "mosque", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "motorcycle bike machine transportation vehicle", "unicode": "&#xf21c;", "name": "motorcycle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mountain glacier hiking hill landscape travel view", "unicode": "&#xf6fc;", "name": "mountain", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mouse click computer cursor input peripheral", "unicode": "&#xf8cc;", "name": "mouse", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mouse-pointer mouse pointer arrow cursor select", "unicode": "&#xf245;", "name": "mouse-pointer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "mug-hot mug hot caliente cocoa coffee cup drink holiday hot chocolate steam tea warmth", "unicode": "&#xf7b6;", "name": "mug-hot", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "music lyrics melody note sing sound", "unicode": "&#xf001;", "name": "music", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "napster", "unicode": "&#xf3d2;", "name": "napster", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "neos", "unicode": "&#xf612;", "name": "neos", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "network-wired wired network computer connect ethernet internet intranet", "unicode": "&#xf6ff;", "name": "network-wired", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "neuter", "unicode": "&#xf22c;", "name": "neuter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "newspaper article editorial headline journal journalism news press", "unicode": "&#xf1ea;", "name": "newspaper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "newspaper article editorial headline journal journalism news press", "unicode": "&#xf1ea;", "name": "newspaper", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "nimblr", "unicode": "&#xf5a8;", "name": "nimblr", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "node.js", "unicode": "&#xf419;", "name": "node", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "node-jsnode.js js", "unicode": "&#xf3d3;", "name": "node-js", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "not-equal not equal arithmetic compare math", "unicode": "&#xf53e;", "name": "not-equal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "notes-medical medical notes clipboard doctor ehr health history records", "unicode": "&#xf481;", "name": "notes-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "npm", "unicode": "&#xf3d4;", "name": "npm", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ns8", "unicode": "&#xf3d5;", "name": "ns8", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "nutritionix", "unicode": "&#xf3d6;", "name": "nutritionix", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "object-group object group combine copy design merge select", "unicode": "&#xf247;", "name": "object-group", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "object-group object group combine copy design merge select", "unicode": "&#xf247;", "name": "object-group", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "object-ungroup object ungroup copy design merge select separate", "unicode": "&#xf248;", "name": "object-ungroup", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "object-ungroup object ungroup copy design merge select separate", "unicode": "&#xf248;", "name": "object-ungroup", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "octopus-deployoctopus deploy", "unicode": "&#xe082;", "name": "octopus-deploy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "odnoklassniki", "unicode": "&#xf263;", "name": "odnoklassniki", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "odnoklassniki-squareodnoklassniki square", "unicode": "&#xf264;", "name": "odnoklassniki-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "oil-can oil can auto crude gasoline grease lubricate petroleum", "unicode": "&#xf613;", "name": "oil-can", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "old-republic old republic politics star wars", "unicode": "&#xf510;", "name": "old-republic", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "om buddhism hinduism jainism mantra", "unicode": "&#xf679;", "name": "om", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "opencart", "unicode": "&#xf23d;", "name": "opencart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "openid", "unicode": "&#xf19b;", "name": "openid", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "opera", "unicode": "&#xf26a;", "name": "opera", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "optin-monsteroptin monster", "unicode": "&#xf23c;", "name": "optin-monster", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "orcid", "unicode": "&#xf8d2;", "name": "orcid", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "osiopen source initiative", "unicode": "&#xf41a;", "name": "osi", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "otter animal badger fauna fur mammal marten", "unicode": "&#xf700;", "name": "otter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "outdent align justify paragraph tab", "unicode": "&#xf03b;", "name": "outdent", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "page4 corporation", "unicode": "&#xf3d7;", "name": "page4", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pagelines eco flora leaf leaves nature plant tree", "unicode": "&#xf18c;", "name": "pagelines", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pager beeper cellphone communication", "unicode": "&#xf815;", "name": "pager", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paint-brush paint brush acrylic art brush color fill paint pigment watercolor", "unicode": "&#xf1fc;", "name": "paint-brush", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paint-roller paint roller acrylic art brush color fill paint pigment watercolor", "unicode": "&#xf5aa;", "name": "paint-roller", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "palette acrylic art brush color fill paint pigment watercolor", "unicode": "&#xf53f;", "name": "palette", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "palfed", "unicode": "&#xf3d8;", "name": "palfed", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pallet archive box inventory shipping warehouse", "unicode": "&#xf482;", "name": "pallet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paper-plane paper plane air float fold mail paper send", "unicode": "&#xf1d8;", "name": "paper-plane", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paper-plane paper plane air float fold mail paper send", "unicode": "&#xf1d8;", "name": "paper-plane", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "paperclip attach attachment connect link", "unicode": "&#xf0c6;", "name": "paperclip", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "parachute-box parachute box aid assistance rescue supplies", "unicode": "&#xf4cd;", "name": "parachute-box", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paragraph edit format text writing", "unicode": "&#xf1dd;", "name": "paragraph", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "parking auto car garage meter", "unicode": "&#xf540;", "name": "parking", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "passport document id identification issued travel", "unicode": "&#xf5ab;", "name": "passport", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pastafarianism agnosticism atheism flying spaghetti monster fsm", "unicode": "&#xf67b;", "name": "pastafarianism", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paste clipboard copy document paper", "unicode": "&#xf0ea;", "name": "paste", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "patreon", "unicode": "&#xf3d9;", "name": "patreon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pause hold wait", "unicode": "&#xf04c;", "name": "pause", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pause-circle pause circle hold wait", "unicode": "&#xf28b;", "name": "pause-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pause-circle pause circle hold wait", "unicode": "&#xf28b;", "name": "pause-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "paw animal cat dog pet print", "unicode": "&#xf1b0;", "name": "paw", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "paypal", "unicode": "&#xf1ed;", "name": "paypal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "peace serenity tranquility truce war", "unicode": "&#xf67c;", "name": "peace", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pen design edit update write", "unicode": "&#xf304;", "name": "pen", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pen-alt alternate pen design edit update write", "unicode": "&#xf305;", "name": "pen-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pen-fancy pen fancy design edit fountain pen update write", "unicode": "&#xf5ac;", "name": "pen-fancy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pen-nib pen nib design edit fountain pen update write", "unicode": "&#xf5ad;", "name": "pen-nib", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pen-square pen square edit pencilsquare update write", "unicode": "&#xf14b;", "name": "pen-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pencil-alt alternate pencil design edit pencil update write", "unicode": "&#xf303;", "name": "pencil-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pencil-ruler pencil ruler design draft draw pencil", "unicode": "&#xf5ae;", "name": "pencil-ruler", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "penny-arcade penny arcade dungeons  dragons dd dnd fantasy game gaming pax tabletop", "unicode": "&#xf704;", "name": "penny-arcade", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "people-arrows people arrows covid19 personal space social distance space spread users", "unicode": "&#xe068;", "name": "people-arrows", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "people-carry people carry box carry fragile help movers package", "unicode": "&#xf4ce;", "name": "people-carry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pepper-hot hot pepper buffalo wings capsicum chili chilli habanero jalapeno mexican spicy tabasco vegetable", "unicode": "&#xf816;", "name": "pepper-hot", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "perbyte", "unicode": "&#xe083;", "name": "perbyte", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "percent discount fraction proportion rate ratio", "unicode": "&#xf295;", "name": "percent", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "percentage discount fraction proportion rate ratio", "unicode": "&#xf541;", "name": "percentage", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "periscope", "unicode": "&#xf3da;", "name": "periscope", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "person-booth person entering booth changing changing room election human person vote voting", "unicode": "&#xf756;", "name": "person-booth", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "phabricator", "unicode": "&#xf3db;", "name": "phabricator", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "phoenix-frameworkphoenix framework", "unicode": "&#xf3dc;", "name": "phoenix-framework", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "phoenix-squadronphoenix squadron", "unicode": "&#xf511;", "name": "phoenix-squadron", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "phone call earphone number support telephone voice", "unicode": "&#xf095;", "name": "phone", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "phone-alt alternate phone call earphone number support telephone voice", "unicode": "&#xf879;", "name": "phone-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "phone-slash phone slash call cancel earphone mute number support telephone voice", "unicode": "&#xf3dd;", "name": "phone-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "phone-square phone square call earphone number support telephone voice", "unicode": "&#xf098;", "name": "phone-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "phone-square-alt alternate phone square call earphone number support telephone voice", "unicode": "&#xf87b;", "name": "phone-square-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "phone-volume phone volume call earphone number sound support telephone voice volumecontrolphone", "unicode": "&#xf2a0;", "name": "phone-volume", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "photo-video photo video av film image library media", "unicode": "&#xf87c;", "name": "photo-video", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "php", "unicode": "&#xf457;", "name": "php", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pied-piperpied piper logo", "unicode": "&#xf2ae;", "name": "pied-piper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pied-piper-altalternate pied piper logo (old)", "unicode": "&#xf1a8;", "name": "pied-piper-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pied-piper-hat pied piper hat (old) clothing", "unicode": "&#xf4e5;", "name": "pied-piper-hat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pied-piper-pppied piper pp logo (old)", "unicode": "&#xf1a7;", "name": "pied-piper-pp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pied-piper-squarepied piper square logo (old)", "unicode": "&#xe01e;", "name": "pied-piper-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "piggy-bank piggy bank bank save savings", "unicode": "&#xf4d3;", "name": "piggy-bank", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pills drugs medicine prescription tablets", "unicode": "&#xf484;", "name": "pills", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pinterest", "unicode": "&#xf0d2;", "name": "pinterest", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pinterest-ppinterest p", "unicode": "&#xf231;", "name": "pinterest-p", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pinterest-squarepinterest square", "unicode": "&#xf0d3;", "name": "pinterest-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "pizza-slice pizza slice cheese chicago italian mozzarella new york pepperoni pie slice teenage mutant ninja turtles tomato", "unicode": "&#xf818;", "name": "pizza-slice", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "place-of-worship place of worship building church holy mosque synagogue", "unicode": "&#xf67f;", "name": "place-of-worship", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plane airplane destination fly location mode travel trip", "unicode": "&#xf072;", "name": "plane", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plane-arrival plane arrival airplane arriving destination fly land landing location mode travel trip", "unicode": "&#xf5af;", "name": "plane-arrival", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plane-departure plane departure airplane departing destination fly location mode take off taking off travel trip", "unicode": "&#xf5b0;", "name": "plane-departure", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plane-slash plane slash airplane mode canceled covid19 delayed grounded travel", "unicode": "&#xe069;", "name": "plane-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "play audio music playing sound start video", "unicode": "&#xf04b;", "name": "play", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "play-circle play circle audio music playing sound start video", "unicode": "&#xf144;", "name": "play-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "play-circle play circle audio music playing sound start video", "unicode": "&#xf144;", "name": "play-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "playstation", "unicode": "&#xf3df;", "name": "playstation", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "plug connect electric online power", "unicode": "&#xf1e6;", "name": "plug", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plus add create expand new positive shape", "unicode": "&#xf067;", "name": "plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plus-circle plus circle add create expand new positive shape", "unicode": "&#xf055;", "name": "plus-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plus-square plus square add create expand new positive shape", "unicode": "&#xf0fe;", "name": "plus-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "plus-square plus square add create expand new positive shape", "unicode": "&#xf0fe;", "name": "plus-square", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "podcast audio broadcast music sound", "unicode": "&#xf2ce;", "name": "podcast", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "poll results survey trend vote voting", "unicode": "&#xf681;", "name": "poll", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "poll-h poll h results survey trend vote voting", "unicode": "&#xf682;", "name": "poll-h", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "poo crap poop shit smile turd", "unicode": "&#xf2fe;", "name": "poo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "poo-storm poo storm bolt cloud euphemism lightning mess poop shit turd", "unicode": "&#xf75a;", "name": "poo-storm", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "poop crap shit smile turd", "unicode": "&#xf619;", "name": "poop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "portrait id image photo picture selfie", "unicode": "&#xf3e0;", "name": "portrait", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pound-sign pound sign currency gbp money", "unicode": "&#xf154;", "name": "pound-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "power-off power off cancel computer on reboot restart", "unicode": "&#xf011;", "name": "power-off", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pray kneel preach religion worship", "unicode": "&#xf683;", "name": "pray", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "praying-hands praying hands kneel preach religion worship", "unicode": "&#xf684;", "name": "praying-hands", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "prescription drugs medical medicine pharmacy rx", "unicode": "&#xf5b1;", "name": "prescription", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "prescription-bottle prescription bottle drugs medical medicine pharmacy rx", "unicode": "&#xf485;", "name": "prescription-bottle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "prescription-bottle-alt alternate prescription bottle drugs medical medicine pharmacy rx", "unicode": "&#xf486;", "name": "prescription-bottle-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "print business copy document office paper", "unicode": "&#xf02f;", "name": "print", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "procedures ekg bed electrocardiogram health hospital life patient vital", "unicode": "&#xf487;", "name": "procedures", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "product-huntproduct hunt", "unicode": "&#xf288;", "name": "product-hunt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "project-diagram project diagram chart graph network pert", "unicode": "&#xf542;", "name": "project-diagram", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pump-medical pump medical antibacterial clean covid19 disinfect hygiene medical grade sanitizer soap", "unicode": "&#xe06a;", "name": "pump-medical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pump-soap pump soap antibacterial clean covid19 disinfect hygiene sanitizer soap", "unicode": "&#xe06b;", "name": "pump-soap", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "pushed", "unicode": "&#xf3e1;", "name": "pushed", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "puzzle-piece puzzle piece addon addon game section", "unicode": "&#xf12e;", "name": "puzzle-piece", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "python", "unicode": "&#xf3e2;", "name": "python", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "qq", "unicode": "&#xf1d6;", "name": "qq", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "qrcode barcode info information scan", "unicode": "&#xf029;", "name": "qrcode", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "question help information support unknown", "unicode": "&#xf128;", "name": "question", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "question-circle question circle help information support unknown", "unicode": "&#xf059;", "name": "question-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "question-circle question circle help information support unknown", "unicode": "&#xf059;", "name": "question-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "quidditch ball bludger broom golden snitch harry potter hogwarts quaffle sport wizard", "unicode": "&#xf458;", "name": "quidditch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "quinscape", "unicode": "&#xf459;", "name": "quinscape", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "quora", "unicode": "&#xf2c4;", "name": "quora", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "quote-left mention note phrase text type", "unicode": "&#xf10d;", "name": "quote-left", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "quote-right mention note phrase text type", "unicode": "&#xf10e;", "name": "quote-right", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "quran book islam muslim religion", "unicode": "&#xf687;", "name": "quran", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "r-projectr project", "unicode": "&#xf4f7;", "name": "r-project", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "radiation danger dangerous deadly hazard nuclear radioactive warning", "unicode": "&#xf7b9;", "name": "radiation", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "radiation-alt alternate radiation danger dangerous deadly hazard nuclear radioactive warning", "unicode": "&#xf7ba;", "name": "radiation-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rainbow gold leprechaun prism rain sky", "unicode": "&#xf75b;", "name": "rainbow", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "random arrows shuffle sort swap switch transfer", "unicode": "&#xf074;", "name": "random", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "raspberry-piraspberry pi", "unicode": "&#xf7bb;", "name": "raspberry-pi", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ravelry", "unicode": "&#xf2d9;", "name": "ravelry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "react", "unicode": "&#xf41b;", "name": "react", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "reacteurope", "unicode": "&#xf75d;", "name": "reacteurope", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "readme", "unicode": "&#xf4d5;", "name": "readme", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "rebel alliance", "unicode": "&#xf1d0;", "name": "rebel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "receipt check invoice money pay table", "unicode": "&#xf543;", "name": "receipt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "record-vinyl record vinyl lp album analog music phonograph sound", "unicode": "&#xf8d9;", "name": "record-vinyl", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "recycle waste compost garbage reuse trash", "unicode": "&#xf1b8;", "name": "recycle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "red-riverred river", "unicode": "&#xf3e3;", "name": "red-river", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "reddit logo", "unicode": "&#xf1a1;", "name": "reddit", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "reddit-alienreddit alien", "unicode": "&#xf281;", "name": "reddit-alien", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "reddit-squarereddit square", "unicode": "&#xf1a2;", "name": "reddit-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "redhat linux operating system os", "unicode": "&#xf7bc;", "name": "redhat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "redo forward refresh reload repeat", "unicode": "&#xf01e;", "name": "redo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "redo-alt alternate redo forward refresh reload repeat", "unicode": "&#xf2f9;", "name": "redo-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "registered trademark copyright mark trademark", "unicode": "&#xf25d;", "name": "registered", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "registered trademark copyright mark trademark", "unicode": "&#xf25d;", "name": "registered", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "remove-format remove format cancel font format remove style text", "unicode": "&#xf87d;", "name": "remove-format", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "renren", "unicode": "&#xf18b;", "name": "renren", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "reply mail message respond", "unicode": "&#xf3e5;", "name": "reply", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "reply-all mail message respond", "unicode": "&#xf122;", "name": "reply-all", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "replyd", "unicode": "&#xf3e6;", "name": "replyd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "republican american conservative election elephant politics republican party right rightwing usa", "unicode": "&#xf75e;", "name": "republican", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "researchgate", "unicode": "&#xf4f8;", "name": "researchgate", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "resolving", "unicode": "&#xf3e7;", "name": "resolving", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "restroom bathroom john loo potty washroom waste wc", "unicode": "&#xf7bd;", "name": "restroom", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "retweet refresh reload share swap", "unicode": "&#xf079;", "name": "retweet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rev.io", "unicode": "&#xf5b2;", "name": "rev", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ribbon badge cause lapel pin", "unicode": "&#xf4d6;", "name": "ribbon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ring dungeons  dragons gollum band binding dd dnd engagement fantasy gold jewelry marriage precious", "unicode": "&#xf70b;", "name": "ring", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "road highway map pavement route street travel", "unicode": "&#xf018;", "name": "road", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "robot android automate computer cyborg", "unicode": "&#xf544;", "name": "robot", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rocket aircraft app jet launch nasa space", "unicode": "&#xf135;", "name": "rocket", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rocketchatrocket.chat", "unicode": "&#xf3e8;", "name": "rocketchat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "rockrms", "unicode": "&#xf3e9;", "name": "rockrms", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "route directions navigation travel", "unicode": "&#xf4d7;", "name": "route", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rss blog feed journal news writing", "unicode": "&#xf09e;", "name": "rss", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rss-square rss square blog feed journal news writing", "unicode": "&#xf143;", "name": "rss-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ruble-sign ruble sign currency money rub", "unicode": "&#xf158;", "name": "ruble-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ruler design draft length measure planning", "unicode": "&#xf545;", "name": "ruler", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ruler-combined ruler combined design draft length measure planning", "unicode": "&#xf546;", "name": "ruler-combined", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ruler-horizontal ruler horizontal design draft length measure planning", "unicode": "&#xf547;", "name": "ruler-horizontal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ruler-vertical ruler vertical design draft length measure planning", "unicode": "&#xf548;", "name": "ruler-vertical", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "running exercise health jog person run sport sprint", "unicode": "&#xf70c;", "name": "running", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rupee-sign indian rupee sign currency indian inr money", "unicode": "&#xf156;", "name": "rupee-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "rust", "unicode": "&#xe07a;", "name": "rust", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sad-cry crying face emoticon face tear tears", "unicode": "&#xf5b3;", "name": "sad-cry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sad-cry crying face emoticon face tear tears", "unicode": "&#xf5b3;", "name": "sad-cry", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "sad-tear loudly crying face emoticon face tear tears", "unicode": "&#xf5b4;", "name": "sad-tear", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sad-tear loudly crying face emoticon face tear tears", "unicode": "&#xf5b4;", "name": "sad-tear", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "safari browser", "unicode": "&#xf267;", "name": "safari", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "salesforce", "unicode": "&#xf83b;", "name": "salesforce", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sass", "unicode": "&#xf41e;", "name": "sass", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "satellite communications hardware orbit space", "unicode": "&#xf7bf;", "name": "satellite", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "satellite-dish satellite dish seti communications hardware receiver saucer signal space", "unicode": "&#xf7c0;", "name": "satellite-dish", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "save disk download floppy floppyo", "unicode": "&#xf0c7;", "name": "save", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "save disk download floppy floppyo", "unicode": "&#xf0c7;", "name": "save", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "schlix", "unicode": "&#xf3ea;", "name": "schlix", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "school building education learn student teacher", "unicode": "&#xf549;", "name": "school", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "screwdriver admin fix mechanic repair settings tool", "unicode": "&#xf54a;", "name": "screwdriver", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "scribd", "unicode": "&#xf28a;", "name": "scribd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "scroll dungeons  dragons announcement dd dnd fantasy paper script", "unicode": "&#xf70e;", "name": "scroll", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sd-card sd card image memory photo save", "unicode": "&#xf7c2;", "name": "sd-card", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "search bigger enlarge find magnify preview zoom", "unicode": "&#xf002;", "name": "search", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "search-dollar search dollar bigger enlarge find magnify money preview zoom", "unicode": "&#xf688;", "name": "search-dollar", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "search-location search location bigger enlarge find magnify preview zoom", "unicode": "&#xf689;", "name": "search-location", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "search-minus search minus minify negative smaller zoom zoom out", "unicode": "&#xf010;", "name": "search-minus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "search-plus search plus bigger enlarge magnify positive zoom zoom in", "unicode": "&#xf00e;", "name": "search-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "searchengin", "unicode": "&#xf3eb;", "name": "searchengin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "seedling flora grow plant vegan", "unicode": "&#xf4d8;", "name": "seedling", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sellcast eercast", "unicode": "&#xf2da;", "name": "sellcast", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sellsy", "unicode": "&#xf213;", "name": "sellsy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "server computer cpu database hardware network", "unicode": "&#xf233;", "name": "server", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "servicestack", "unicode": "&#xf3ec;", "name": "servicestack", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "shapes blocks build circle square triangle", "unicode": "&#xf61f;", "name": "shapes", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "share forward save send social", "unicode": "&#xf064;", "name": "share", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "share-alt alternate share forward save send social", "unicode": "&#xf1e0;", "name": "share-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "share-alt-square alternate share square forward save send social", "unicode": "&#xf1e1;", "name": "share-alt-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "share-square share square forward save send social", "unicode": "&#xf14d;", "name": "share-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "share-square share square forward save send social", "unicode": "&#xf14d;", "name": "share-square", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "shekel-sign shekel sign currency ils money", "unicode": "&#xf20b;", "name": "shekel-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shield-alt alternate shield achievement award block defend security winner", "unicode": "&#xf3ed;", "name": "shield-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shield-virus shield virus antibodies barrier covid19 health protect", "unicode": "&#xe06c;", "name": "shield-virus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ship boat sea water", "unicode": "&#xf21a;", "name": "ship", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shipping-fast shipping fast express fedex mail overnight package ups", "unicode": "&#xf48b;", "name": "shipping-fast", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shirtsinbulkshirts in bulk", "unicode": "&#xf214;", "name": "shirtsinbulk", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "shoe-prints shoe prints feet footprints steps walk", "unicode": "&#xf54b;", "name": "shoe-prints", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shopify", "unicode": "&#xe057;", "name": "shopify", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "shopping-bag shopping bag buy checkout grocery payment purchase", "unicode": "&#xf290;", "name": "shopping-bag", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shopping-basket shopping basket buy checkout grocery payment purchase", "unicode": "&#xf291;", "name": "shopping-basket", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shopping-cart buy checkout grocery payment purchase", "unicode": "&#xf07a;", "name": "shopping-cart", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shopware", "unicode": "&#xf5b5;", "name": "shopware", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "shower bath clean faucet water", "unicode": "&#xf2cc;", "name": "shower", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "shuttle-van shuttle van airport machine publictransportation transportation travel vehicle", "unicode": "&#xf5b6;", "name": "shuttle-van", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sign directions real estate signage wayfinding", "unicode": "&#xf4d9;", "name": "sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sign-in-alt alternate sign in arrow enter join log in login sign in sign up signin signin signup", "unicode": "&#xf2f6;", "name": "sign-in-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sign-language sign language translate asl deaf hands", "unicode": "&#xf2a7;", "name": "sign-language", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sign-out-alt alternate sign out arrow exit leave log out logout signout", "unicode": "&#xf2f5;", "name": "sign-out-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "signal bars graph online reception status", "unicode": "&#xf012;", "name": "signal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "signature john hancock cursive name writing", "unicode": "&#xf5b7;", "name": "signature", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sim-card sim card hard drive hardware portable storage technology tiny", "unicode": "&#xf7c4;", "name": "sim-card", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "simplybuilt", "unicode": "&#xf215;", "name": "simplybuilt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sink bathroom covid19 faucet kitchen wash", "unicode": "&#xe06d;", "name": "sink", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sistrix", "unicode": "&#xf3ee;", "name": "sistrix", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sitemap directory hierarchy ia information architecture organization", "unicode": "&#xf0e8;", "name": "sitemap", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sith", "unicode": "&#xf512;", "name": "sith", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "skating activity figure skating fitness ice person winter", "unicode": "&#xf7c5;", "name": "skating", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sketch app design interface", "unicode": "&#xf7c6;", "name": "sketch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "skiing activity downhill fast fitness olympics outdoors person seasonal slalom", "unicode": "&#xf7c9;", "name": "skiing", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "skiing-nordic skiing nordic activity cross country fitness outdoors person seasonal", "unicode": "&#xf7ca;", "name": "skiing-nordic", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "skull bones skeleton xray yorick", "unicode": "&#xf54c;", "name": "skull", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "skull-crossbones skull & crossbones dungeons  dragons alert bones dd danger dead deadly death dnd fantasy halloween holiday jollyroger pirate poison skeleton warning", "unicode": "&#xf714;", "name": "skull-crossbones", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "skyatlas", "unicode": "&#xf216;", "name": "skyatlas", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "skype", "unicode": "&#xf17e;", "name": "skype", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "slack logo anchor hash hashtag", "unicode": "&#xf198;", "name": "slack", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "slack-hash slack hashtag anchor hash hashtag", "unicode": "&#xf3ef;", "name": "slack-hash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "slash cancel close mute off stop x", "unicode": "&#xf715;", "name": "slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sleigh christmas claus fly holiday santa sled snow xmas", "unicode": "&#xf7cc;", "name": "sleigh", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sliders-h horizontal sliders adjust settings sliders toggle", "unicode": "&#xf1de;", "name": "sliders-h", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "slideshare", "unicode": "&#xf1e7;", "name": "slideshare", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "smile smiling face approve emoticon face happy rating satisfied", "unicode": "&#xf118;", "name": "smile", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "smile smiling face approve emoticon face happy rating satisfied", "unicode": "&#xf118;", "name": "smile", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "smile-beam beaming face with smiling eyes emoticon face happy positive", "unicode": "&#xf5b8;", "name": "smile-beam", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "smile-beam beaming face with smiling eyes emoticon face happy positive", "unicode": "&#xf5b8;", "name": "smile-beam", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "smile-wink winking face emoticon face happy hint joke", "unicode": "&#xf4da;", "name": "smile-wink", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "smile-wink winking face emoticon face happy hint joke", "unicode": "&#xf4da;", "name": "smile-wink", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "smog dragon fog haze pollution smoke weather", "unicode": "&#xf75f;", "name": "smog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "smoking cancer cigarette nicotine smoking status tobacco", "unicode": "&#xf48d;", "name": "smoking", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "smoking-ban smoking ban ban cancel no smoking nonsmoking", "unicode": "&#xf54d;", "name": "smoking-ban", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sms chat conversation message mobile notification phone texting", "unicode": "&#xf7cd;", "name": "sms", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "snapchat", "unicode": "&#xf2ab;", "name": "snapchat", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "snapchat-ghosts<PERSON><PERSON><PERSON> ghost", "unicode": "&#xf2ac;", "name": "snapchat-ghost", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "snapchat-squaresnapchat square", "unicode": "&#xf2ad;", "name": "snapchat-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "snowboarding activity fitness olympics outdoors person", "unicode": "&#xf7ce;", "name": "snowboarding", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "snowflake precipitation rain winter", "unicode": "&#xf2dc;", "name": "snowflake", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "snowflake precipitation rain winter", "unicode": "&#xf2dc;", "name": "snowflake", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "snowman decoration frost frosty holiday", "unicode": "&#xf7d0;", "name": "snowman", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "snowplow clean up cold road storm winter", "unicode": "&#xf7d2;", "name": "snowplow", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "soap bubbles clean covid19 hygiene wash", "unicode": "&#xe06e;", "name": "soap", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "socks business socks business time clothing feet flight of the conchords wednesday", "unicode": "&#xf696;", "name": "socks", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "solar-panel solar panel clean ecofriendly energy green sun", "unicode": "&#xf5ba;", "name": "solar-panel", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort filter order", "unicode": "&#xf0dc;", "name": "sort", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-alpha-down sort alphabetical down alphabetical arrange filter order sortalphaasc", "unicode": "&#xf15d;", "name": "sort-alpha-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-alpha-down-alt alternate sort alphabetical down alphabetical arrange filter order sortalphaasc", "unicode": "&#xf881;", "name": "sort-alpha-down-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-alpha-up sort alphabetical up alphabetical arrange filter order sortalphadesc", "unicode": "&#xf15e;", "name": "sort-alpha-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-alpha-up-alt alternate sort alphabetical up alphabetical arrange filter order sortalphadesc", "unicode": "&#xf882;", "name": "sort-alpha-up-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-amount-down sort amount down arrange filter number order sortamountasc", "unicode": "&#xf160;", "name": "sort-amount-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-amount-down-alt alternate sort amount down arrange filter order sortamountasc", "unicode": "&#xf884;", "name": "sort-amount-down-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-amount-up sort amount up arrange filter order sortamountdesc", "unicode": "&#xf161;", "name": "sort-amount-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-amount-up-alt alternate sort amount up arrange filter order sortamountdesc", "unicode": "&#xf885;", "name": "sort-amount-up-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-down sort down (descending) arrow descending filter order sortdesc", "unicode": "&#xf0dd;", "name": "sort-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-numeric-down sort numeric down arrange filter numbers order sortnumericasc", "unicode": "&#xf162;", "name": "sort-numeric-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-numeric-down-alt alternate sort numeric down arrange filter numbers order sortnumericasc", "unicode": "&#xf886;", "name": "sort-numeric-down-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-numeric-up sort numeric up arrange filter numbers order sortnumericdesc", "unicode": "&#xf163;", "name": "sort-numeric-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-numeric-up-alt alternate sort numeric up arrange filter numbers order sortnumericdesc", "unicode": "&#xf887;", "name": "sort-numeric-up-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sort-up sort up (ascending) arrow ascending filter order sortasc", "unicode": "&#xf0de;", "name": "sort-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "soundcloud", "unicode": "&#xf1be;", "name": "soundcloud", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sourcetree", "unicode": "&#xf7d3;", "name": "sourcetree", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "spa flora massage mindfulness plant wellness", "unicode": "&#xf5bb;", "name": "spa", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "space-shuttle space shuttle astronaut machine nasa rocket space transportation", "unicode": "&#xf197;", "name": "space-shuttle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "speakap", "unicode": "&#xf3f3;", "name": "speakap", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "speaker-deckspeaker deck", "unicode": "&#xf83c;", "name": "speaker-deck", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "spell-check spell check dictionary edit editor grammar text", "unicode": "&#xf891;", "name": "spell-check", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "spider arachnid bug charlotte crawl eight halloween", "unicode": "&#xf717;", "name": "spider", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "spinner circle loading progress", "unicode": "&#xf110;", "name": "spinner", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "splotch ink blob blotch glob stain", "unicode": "&#xf5bc;", "name": "splotch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "spotify", "unicode": "&#xf1bc;", "name": "spotify", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "spray-can spray can paint aerosol design graffiti tag", "unicode": "&#xf5bd;", "name": "spray-can", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "square block box shape", "unicode": "&#xf0c8;", "name": "square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "square block box shape", "unicode": "&#xf0c8;", "name": "square", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "square-full square full block box shape", "unicode": "&#xf45c;", "name": "square-full", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "square-root-alt alternate square root arithmetic calculus division math", "unicode": "&#xf698;", "name": "square-root-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "squarespace", "unicode": "&#xf5be;", "name": "squarespace", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stack-exchangestack exchange", "unicode": "&#xf18d;", "name": "stack-exchange", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stack-overflowstack overflow", "unicode": "&#xf16c;", "name": "stack-overflow", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stackpath", "unicode": "&#xf842;", "name": "stackpath", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stamp art certificate imprint rubber seal", "unicode": "&#xf5bf;", "name": "stamp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "star achievement award favorite important night rating score", "unicode": "&#xf005;", "name": "star", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "star achievement award favorite important night rating score", "unicode": "&#xf005;", "name": "star", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "star-and-crescent star and crescent islam muslim religion", "unicode": "&#xf699;", "name": "star-and-crescent", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "star-half achievement award rating score starhalfempty starhalff<PERSON>", "unicode": "&#xf089;", "name": "star-half", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "star-half achievement award rating score starhalfempty starhalff<PERSON>", "unicode": "&#xf089;", "name": "star-half", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "star-half-alt alternate star half achievement award rating score starhalfempty star<PERSON><PERSON><PERSON>", "unicode": "&#xf5c0;", "name": "star-half-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "star-of-david star of david jewish judaism religion", "unicode": "&#xf69a;", "name": "star-of-david", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "star-of-life star of life doctor emt first aid health medical", "unicode": "&#xf621;", "name": "star-of-life", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "staylinked", "unicode": "&#xf3f5;", "name": "staylinked", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "steam", "unicode": "&#xf1b6;", "name": "steam", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "steam-squaresteam square", "unicode": "&#xf1b7;", "name": "steam-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "steam-symbolsteam symbol", "unicode": "&#xf3f6;", "name": "steam-symbol", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "step-backward beginning first previous rewind start", "unicode": "&#xf048;", "name": "step-backward", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "step-forward end last next", "unicode": "&#xf051;", "name": "step-forward", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "stethoscope covid19 diagnosis doctor general practitioner hospital infirmary medicine office outpatient", "unicode": "&#xf0f1;", "name": "stethoscope", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sticker-mulesticker mule", "unicode": "&#xf3f7;", "name": "sticker-mule", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "sticky-note sticky note message note paper reminder sticker", "unicode": "&#xf249;", "name": "sticky-note", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sticky-note sticky note message note paper reminder sticker", "unicode": "&#xf249;", "name": "sticky-note", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "stop block box square", "unicode": "&#xf04d;", "name": "stop", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "stop-circle stop circle block box circle square", "unicode": "&#xf28d;", "name": "stop-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "stop-circle stop circle block box circle square", "unicode": "&#xf28d;", "name": "stop-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "stopwatch clock reminder time", "unicode": "&#xf2f2;", "name": "stopwatch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "stopwatch-20 stopwatch 20 abcs countdown covid19 happy birthday i will survive reminder seconds time timer", "unicode": "&#xe06f;", "name": "stopwatch-20", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "store building buy purchase shopping", "unicode": "&#xf54e;", "name": "store", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "store-alt alternate store building buy purchase shopping", "unicode": "&#xf54f;", "name": "store-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "store-alt-slash alternate store slash building buy closed covid19 purchase shopping", "unicode": "&#xe070;", "name": "store-alt-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "store-slash store slash building buy closed covid19 purchase shopping", "unicode": "&#xe071;", "name": "store-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "strava", "unicode": "&#xf428;", "name": "strava", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stream flow list timeline", "unicode": "&#xf550;", "name": "stream", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "street-view street view directions location map navigation", "unicode": "&#xf21d;", "name": "street-view", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "strikethrough cancel edit font format text type", "unicode": "&#xf0cc;", "name": "strikethrough", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "stripe", "unicode": "&#xf429;", "name": "stripe", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stripe-sstripe s", "unicode": "&#xf42a;", "name": "stripe-s", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stroopwafel caramel cookie dessert sweets waffle", "unicode": "&#xf551;", "name": "stro<PERSON><PERSON><PERSON>l", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "studiovina<PERSON><PERSON><PERSON> vinari", "unicode": "&#xf3f8;", "name": "<PERSON><PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stumbleupon logo", "unicode": "&#xf1a4;", "name": "stumbleupon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "stumbleupon-circlestumbleupon circle", "unicode": "&#xf1a3;", "name": "stumbleupon-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "subscript edit font format text type", "unicode": "&#xf12c;", "name": "subscript", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "subway machine railway train transportation vehicle", "unicode": "&#xf239;", "name": "subway", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "suitcase baggage luggage move travel trip", "unicode": "&#xf0f2;", "name": "suitcase", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "suitcase-rolling suitcase rolling baggage luggage move suitcase travel trip", "unicode": "&#xf5c1;", "name": "suitcase-rolling", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sun brighten contrast day lighter sol solar star weather", "unicode": "&#xf185;", "name": "sun", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sun brighten contrast day lighter sol solar star weather", "unicode": "&#xf185;", "name": "sun", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "superpowers", "unicode": "&#xf2dd;", "name": "superpowers", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "superscript edit exponential font format text type", "unicode": "&#xf12b;", "name": "superscript", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "supple", "unicode": "&#xf3f9;", "name": "supple", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "surprise hushed face emoticon face shocked", "unicode": "&#xf5c2;", "name": "surprise", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "surprise hushed face emoticon face shocked", "unicode": "&#xf5c2;", "name": "surprise", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "suse linux operating system os", "unicode": "&#xf7d6;", "name": "suse", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "swatchbook pantone color design hue palette", "unicode": "&#xf5c3;", "name": "swatchbook", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "swift", "unicode": "&#xf8e1;", "name": "swift", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "swimmer athlete head man olympics person pool water", "unicode": "&#xf5c4;", "name": "swimmer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "swimming-pool swimming pool ladder recreation swim water", "unicode": "&#xf5c5;", "name": "swimming-pool", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "symfony", "unicode": "&#xf83d;", "name": "symfony", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "synagogue building jewish judaism religion star of david temple", "unicode": "&#xf69b;", "name": "synagogue", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sync exchange refresh reload rotate swap", "unicode": "&#xf021;", "name": "sync", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "sync-alt alternate sync exchange refresh reload rotate swap", "unicode": "&#xf2f1;", "name": "sync-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "syringe covid19 doctor immunizations medical needle", "unicode": "&#xf48e;", "name": "syringe", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "table data excel spreadsheet", "unicode": "&#xf0ce;", "name": "table", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "table-tennis table tennis ball paddle ping pong", "unicode": "&#xf45d;", "name": "table-tennis", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tablet apple device ipad kindle screen", "unicode": "&#xf10a;", "name": "tablet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tablet-alt alternate tablet apple device ipad kindle screen", "unicode": "&#xf3fa;", "name": "tablet-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tablets drugs medicine pills prescription", "unicode": "&#xf490;", "name": "tablets", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tachometer-alt alternate tachometer dashboard fast odometer speed speedometer", "unicode": "&#xf3fd;", "name": "tachometer-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tag discount label price shopping", "unicode": "&#xf02b;", "name": "tag", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tags discount label price shopping", "unicode": "&#xf02c;", "name": "tags", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tape design package sticky", "unicode": "&#xf4db;", "name": "tape", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tasks checklist downloading downloads loading progress project management settings to do", "unicode": "&#xf0ae;", "name": "tasks", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "taxi cab cabbie car car service lyft machine transportation travel uber vehicle", "unicode": "&#xf1ba;", "name": "taxi", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "teamspeak", "unicode": "&#xf4f9;", "name": "teamspeak", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "teeth bite dental dentist gums mouth smile tooth", "unicode": "&#xf62e;", "name": "teeth", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "teeth-open teeth open dental dentist gums bite mouth smile tooth", "unicode": "&#xf62f;", "name": "teeth-open", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "telegram", "unicode": "&#xf2c6;", "name": "telegram", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "telegram-planetelegram plane", "unicode": "&#xf3fe;", "name": "telegram-plane", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "temperature-high high temperature cook covid19 mercury summer thermometer warm", "unicode": "&#xf769;", "name": "temperature-high", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "temperature-low low temperature cold cool covid19 mercury thermometer winter", "unicode": "&#xf76b;", "name": "temperature-low", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tencent-weibotencent weibo", "unicode": "&#xf1d5;", "name": "tencent-weibo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "tenge currency kazakhstan money price", "unicode": "&#xf7d7;", "name": "tenge", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "terminal code command console development prompt", "unicode": "&#xf120;", "name": "terminal", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "text-height edit font format text type", "unicode": "&#xf034;", "name": "text-height", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "text-width text width edit font format text type", "unicode": "&#xf035;", "name": "text-width", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "th blocks boxes grid squares", "unicode": "&#xf00a;", "name": "th", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "th-large blocks boxes grid squares", "unicode": "&#xf009;", "name": "th-large", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "th-list checklist completed done finished ol todo ul", "unicode": "&#xf00b;", "name": "th-list", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "the-red-yetithe red yeti", "unicode": "&#xf69d;", "name": "the-red-yeti", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "theater-masks theater masks comedy perform theatre tragedy", "unicode": "&#xf630;", "name": "theater-masks", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "themeco", "unicode": "&#xf5c6;", "name": "themeco", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "<PERSON><PERSON><PERSON>", "unicode": "&#xf2b2;", "name": "<PERSON><PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "thermometer covid19 mercury status temperature", "unicode": "&#xf491;", "name": "thermometer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thermometer-empty thermometer empty cold mercury status temperature", "unicode": "&#xf2cb;", "name": "thermometer-empty", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thermometer-full thermometer full fever hot mercury status temperature", "unicode": "&#xf2c7;", "name": "thermometer-full", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thermometer-half thermometer 1/2 full mercury status temperature", "unicode": "&#xf2c9;", "name": "thermometer-half", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thermometer-quarter thermometer 1/4 full mercury status temperature", "unicode": "&#xf2ca;", "name": "thermometer-quarter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thermometer-three-quarters thermometer 3/4 full mercury status temperature", "unicode": "&#xf2c8;", "name": "thermometer-three-quarters", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "think-peaksthink peaks", "unicode": "&#xf731;", "name": "think-peaks", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "thumbs-down disagree disapprove dislike hand social thumbsodown", "unicode": "&#xf165;", "name": "thumbs-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thumbs-down disagree disapprove dislike hand social thumbsodown", "unicode": "&#xf165;", "name": "thumbs-down", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "thumbs-up agree approve favorite hand like ok okay social success thumbsoup yes you got it dude", "unicode": "&#xf164;", "name": "thumbs-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "thumbs-up agree approve favorite hand like ok okay social success thumbsoup yes you got it dude", "unicode": "&#xf164;", "name": "thumbs-up", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "thumbtack coordinates location marker pin", "unicode": "&#xf08d;", "name": "thumbtack", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ticket-alt alternate ticket movie pass support ticket", "unicode": "&#xf3ff;", "name": "ticket-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tiktok", "unicode": "&#xe07b;", "name": "tiktok", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "times close cross error exit incorrect notice notification notify problem wrong x", "unicode": "&#xf00d;", "name": "times", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "times-circle times circle close cross exit incorrect notice notification notify problem wrong x", "unicode": "&#xf057;", "name": "times-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "times-circle times circle close cross exit incorrect notice notification notify problem wrong x", "unicode": "&#xf057;", "name": "times-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "tint color drop droplet raindrop waterdrop", "unicode": "&#xf043;", "name": "tint", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tint-slash tint slash color drop droplet raindrop waterdrop", "unicode": "&#xf5c7;", "name": "tint-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tired face angry emoticon face grumpy upset", "unicode": "&#xf5c8;", "name": "tired", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tired face angry emoticon face grumpy upset", "unicode": "&#xf5c8;", "name": "tired", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "toggle-off toggle off switch", "unicode": "&#xf204;", "name": "toggle-off", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "toggle-on toggle on switch", "unicode": "&#xf205;", "name": "toggle-on", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "toilet bathroom flush john loo pee plumbing poop porcelain potty restroom throne washroom waste wc", "unicode": "&#xf7d8;", "name": "toilet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "toilet-paper toilet paper bathroom covid19 halloween holiday lavatory prank restroom roll", "unicode": "&#xf71e;", "name": "toilet-paper", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "toilet-paper-slash toilet paper slash bathroom covid19 halloween holiday lavatory leaves prank restroom roll trouble ut oh", "unicode": "&#xe072;", "name": "toilet-paper-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "toolbox admin container fix repair settings tools", "unicode": "&#xf552;", "name": "toolbox", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tools admin fix repair screwdriver settings wrench", "unicode": "&#xf7d9;", "name": "tools", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tooth bicuspid dental dentist molar mouth teeth", "unicode": "&#xf5c9;", "name": "tooth", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "torah book jewish judaism religion scroll", "unicode": "&#xf6a0;", "name": "torah", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "torii-gate torii gate building shintoism", "unicode": "&#xf6a1;", "name": "torii-gate", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tractor agriculture farm vehicle", "unicode": "&#xf722;", "name": "tractor", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trade-federationtrade federation", "unicode": "&#xf513;", "name": "trade-federation", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "trademark copyright register symbol", "unicode": "&#xf25c;", "name": "trademark", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "traffic-light traffic light direction road signal travel", "unicode": "&#xf637;", "name": "traffic-light", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trailer carry haul moving travel", "unicode": "&#xe041;", "name": "trailer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "train bullet commute locomotive railway subway", "unicode": "&#xf238;", "name": "train", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tram crossing machine mountains seasonal transportation", "unicode": "&#xf7da;", "name": "tram", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "transgender intersex", "unicode": "&#xf224;", "name": "transgender", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "transgender-alt alternate transgender intersex", "unicode": "&#xf225;", "name": "transgender-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trash delete garbage hide remove", "unicode": "&#xf1f8;", "name": "trash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trash-alt alternate trash delete garbage hide remove trasho", "unicode": "&#xf2ed;", "name": "trash-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trash-alt alternate trash delete garbage hide remove trasho", "unicode": "&#xf2ed;", "name": "trash-alt", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "trash-restore trash restore back control z oops undo", "unicode": "&#xf829;", "name": "trash-restore", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trash-restore-alt alternative trash restore back control z oops undo", "unicode": "&#xf82a;", "name": "trash-restore-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tree bark fall flora forest nature plant seasonal", "unicode": "&#xf1bb;", "name": "tree", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "trello atlassian", "unicode": "&#xf181;", "name": "trello", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "tripadvisor", "unicode": "&#xf262;", "name": "tripadvisor", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "trophy achievement award cup game winner", "unicode": "&#xf091;", "name": "trophy", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "truck cargo delivery shipping vehicle", "unicode": "&#xf0d1;", "name": "truck", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "truck-loading truck loading box cargo delivery inventory moving rental vehicle", "unicode": "&#xf4de;", "name": "truck-loading", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "truck-monster truck monster offroad vehicle wheel", "unicode": "&#xf63b;", "name": "truck-monster", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "truck-moving truck moving cargo inventory rental vehicle", "unicode": "&#xf4df;", "name": "truck-moving", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "truck-pickup truck side cargo vehicle", "unicode": "&#xf63c;", "name": "truck-pickup", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tshirt t-shirt clothing fashion garment shirt", "unicode": "&#xf553;", "name": "tshirt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tty communication deaf telephone teletypewriter text", "unicode": "&#xf1e4;", "name": "tty", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "tumblr", "unicode": "&#xf173;", "name": "tumblr", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "tumblr-squaretumblr square", "unicode": "&#xf174;", "name": "tumblr-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "tv television computer display monitor", "unicode": "&#xf26c;", "name": "tv", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "twitch", "unicode": "&#xf1e8;", "name": "twitch", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "twitter social network tweet", "unicode": "&#xf099;", "name": "twitter", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "twitter-square twitter square social network tweet", "unicode": "&#xf081;", "name": "twitter-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "typo3", "unicode": "&#xf42b;", "name": "typo3", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "uber", "unicode": "&#xf402;", "name": "uber", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ubuntu linux operating system os", "unicode": "&#xf7df;", "name": "ubuntu", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "uikit", "unicode": "&#xf403;", "name": "uikit", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "umbraco", "unicode": "&#xf8e8;", "name": "umbraco", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "umbrella protection rain storm wet", "unicode": "&#xf0e9;", "name": "umbrella", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "umbrella-beach umbrella beach protection recreation sand shade summer sun", "unicode": "&#xf5ca;", "name": "umbrella-beach", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "uncharted software", "unicode": "&#xe084;", "name": "uncharted", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "underline edit emphasis format text writing", "unicode": "&#xf0cd;", "name": "underline", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "undo back control z exchange oops return rotate swap", "unicode": "&#xf0e2;", "name": "undo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "undo-alt alternate undo back control z exchange oops return swap", "unicode": "&#xf2ea;", "name": "undo-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "uniregistry", "unicode": "&#xf404;", "name": "uniregistry", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "unity 3d", "unicode": "&#xe049;", "name": "unity", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "universal-access universal access accessibility hearing person seeing visual impairment", "unicode": "&#xf29a;", "name": "universal-access", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "university bank building college higher education  students institution", "unicode": "&#xf19c;", "name": "university", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "unlink attachment chain chainbroken remove", "unicode": "&#xf127;", "name": "unlink", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "unlock admin lock password private protect", "unicode": "&#xf09c;", "name": "unlock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "unlock-alt alternate unlock admin lock password private protect", "unicode": "&#xf13e;", "name": "unlock-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "unsplash", "unicode": "&#xe07c;", "name": "unsplash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "untappd", "unicode": "&#xf405;", "name": "untappd", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "upload hard drive import publish", "unicode": "&#xf093;", "name": "upload", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "ups united parcel service package shipping", "unicode": "&#xf7e0;", "name": "ups", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "usb", "unicode": "&#xf287;", "name": "usb", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "user account avatar head human man person profile", "unicode": "&#xf007;", "name": "user", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user account avatar head human man person profile", "unicode": "&#xf007;", "name": "user", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "user-alt alternate user account avatar head human man person profile", "unicode": "&#xf406;", "name": "user-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-alt-slash alternate user slash account avatar head human man person profile", "unicode": "&#xf4fa;", "name": "user-alt-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-astronaut user astronaut avatar clothing cosmonaut nasa space suit", "unicode": "&#xf4fb;", "name": "user-astronaut", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-check user check accept check person verified", "unicode": "&#xf4fc;", "name": "user-check", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-circle user circle account avatar head human man person profile", "unicode": "&#xf2bd;", "name": "user-circle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-circle user circle account avatar head human man person profile", "unicode": "&#xf2bd;", "name": "user-circle", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "user-clock user clock alert person remind time", "unicode": "&#xf4fd;", "name": "user-clock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-cog user cog admin cog person settings", "unicode": "&#xf4fe;", "name": "user-cog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-edit user edit edit pen pencil person update write", "unicode": "&#xf4ff;", "name": "user-edit", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-friends user friends group people person team users", "unicode": "&#xf500;", "name": "user-friends", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-graduate user graduate cap clothing commencement gown graduation person student", "unicode": "&#xf501;", "name": "user-graduate", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-injured user injured cast injury ouch patient person sling", "unicode": "&#xf728;", "name": "user-injured", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-lock user lock admin lock person private unlock", "unicode": "&#xf502;", "name": "user-lock", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-md doctor covid19 job medical nurse occupation physician profile surgeon", "unicode": "&#xf0f0;", "name": "user-md", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-minus user minus delete negative remove", "unicode": "&#xf503;", "name": "user-minus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-ninja user ninja assassin avatar dangerous deadly sneaky", "unicode": "&#xf504;", "name": "user-ninja", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-nurse nurse covid19 doctor midwife practitioner surgeon", "unicode": "&#xf82f;", "name": "user-nurse", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-plus user plus add avatar positive sign up signup team", "unicode": "&#xf234;", "name": "user-plus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-secret user secret clothing coat hat incognito person privacy spy whisper", "unicode": "&#xf21b;", "name": "user-secret", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-shield user shield admin person private protect safe", "unicode": "&#xf505;", "name": "user-shield", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-slash user slash ban delete remove", "unicode": "&#xf506;", "name": "user-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-tag user tag avatar discount label person role special", "unicode": "&#xf507;", "name": "user-tag", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-tie user tie avatar business clothing formal professional suit", "unicode": "&#xf508;", "name": "user-tie", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "user-times remove user archive delete remove x", "unicode": "&#xf235;", "name": "user-times", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "users friends group people persons profiles team", "unicode": "&#xf0c0;", "name": "users", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "users-cog users cog admin cog group person settings team", "unicode": "&#xf509;", "name": "users-cog", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "users-slash users slash disband friends group people persons profiles separate team ungroup", "unicode": "&#xe073;", "name": "users-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "usps united states postal service american package shipping usa", "unicode": "&#xf7e1;", "name": "usps", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "ussunnahus-sunnah foundation", "unicode": "&#xf407;", "name": "ussunnah", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "utensil-spoon utensil spoon cutlery dining scoop silverware spoon", "unicode": "&#xf2e5;", "name": "utensil-spoon", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "utensils cutlery dining dinner eat food fork knife restaurant", "unicode": "&#xf2e7;", "name": "utensils", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vaadin", "unicode": "&#xf408;", "name": "vaadin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "vector-square vector square anchors lines object render shape", "unicode": "&#xf5cb;", "name": "vector-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "venus female", "unicode": "&#xf221;", "name": "venus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "venus-double venus double female", "unicode": "&#xf226;", "name": "venus-double", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "venus-mars venus mars gender", "unicode": "&#xf228;", "name": "venus-mars", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vest biker fashion style", "unicode": "&#xe085;", "name": "vest", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vest-patches biker fashion style", "unicode": "&#xe086;", "name": "vest-patches", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "viacoin", "unicode": "&#xf237;", "name": "viacoin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "viadeo", "unicode": "&#xf2a9;", "name": "viadeo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "viadeo-squareviadeo square", "unicode": "&#xf2aa;", "name": "viadeo-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "vial experiment lab sample science test test tube", "unicode": "&#xf492;", "name": "vial", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vials experiment lab sample science test test tube", "unicode": "&#xf493;", "name": "vials", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "viber", "unicode": "&#xf409;", "name": "viber", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "video camera film movie record videocamera", "unicode": "&#xf03d;", "name": "video", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "video-slash video slash add create film new positive record video", "unicode": "&#xf4e2;", "name": "video-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vihara buddhism buddhist building monastery", "unicode": "&#xf6a7;", "name": "vihara", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vimeo", "unicode": "&#xf40a;", "name": "vimeo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "vimeo-squarevimeo square", "unicode": "&#xf194;", "name": "vimeo-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "vimeo-vvimeo", "unicode": "&#xf27d;", "name": "vimeo-v", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "vine", "unicode": "&#xf1ca;", "name": "vine", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "virus bug covid19 flu health sick viral", "unicode": "&#xe074;", "name": "virus", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "virus-slash virus slash bug covid19 cure eliminate flu health sick viral", "unicode": "&#xe075;", "name": "virus-slash", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "viruses bugs covid19 flu health multiply sick spread viral", "unicode": "&#xe076;", "name": "viruses", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vk", "unicode": "&#xf189;", "name": "vk", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "vnv", "unicode": "&#xf40b;", "name": "vnv", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "voicemail answer inbox message phone", "unicode": "&#xf897;", "name": "voicemail", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "volleyball-ball volleyball ball beach olympics sport", "unicode": "&#xf45f;", "name": "volleyball-ball", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "volume-down volume down audio lower music quieter sound speaker", "unicode": "&#xf027;", "name": "volume-down", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "volume-mute volume mute audio music quiet sound speaker", "unicode": "&#xf6a9;", "name": "volume-mute", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "volume-off volume off audio ban music mute quiet silent sound", "unicode": "&#xf026;", "name": "volume-off", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "volume-up volume up audio higher louder music sound speaker", "unicode": "&#xf028;", "name": "volume-up", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vote-yea vote yea accept cast election politics positive yes", "unicode": "&#xf772;", "name": "vote-yea", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vr-cardboard cardboard vr 3d augment google reality virtual", "unicode": "&#xf729;", "name": "vr-cardboard", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "vuejsvue.js", "unicode": "&#xf41f;", "name": "v<PERSON><PERSON><PERSON>", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "walking exercise health pedometer person steps", "unicode": "&#xf554;", "name": "walking", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wallet billfold cash currency money", "unicode": "&#xf555;", "name": "wallet", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "warehouse building capacity garage inventory storage", "unicode": "&#xf494;", "name": "warehouse", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "watchman-monitoringwatchman monitoring", "unicode": "&#xe087;", "name": "watchman-monitoring", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "water lake liquid ocean sea swim wet", "unicode": "&#xf773;", "name": "water", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wave-square square wave frequency pulse signal", "unicode": "&#xf83e;", "name": "wave-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "waze", "unicode": "&#xf83f;", "name": "waze", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "weebly", "unicode": "&#xf5cc;", "name": "weebly", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "weibo", "unicode": "&#xf18a;", "name": "weibo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "weight health measurement scale", "unicode": "&#xf496;", "name": "weight", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "weight-hanging hanging weight anvil heavy measurement", "unicode": "&#xf5cd;", "name": "weight-hanging", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "weixin (wechat)", "unicode": "&#xf1d7;", "name": "weixin", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "whatsappwhat's app", "unicode": "&#xf232;", "name": "whatsapp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "whatsapp-squarewhat's app square", "unicode": "&#xf40c;", "name": "whatsapp-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wheelchair accessible handicap person", "unicode": "&#xf193;", "name": "wheelchair", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "whmcs", "unicode": "&#xf40d;", "name": "whmcs", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wifi connection hotspot internet network wireless", "unicode": "&#xf1eb;", "name": "wifi", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wikipedia-wwikipedia w", "unicode": "&#xf266;", "name": "wikipedia-w", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wind air blow breeze fall seasonal weather", "unicode": "&#xf72e;", "name": "wind", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "window-close window close browser cancel computer development", "unicode": "&#xf410;", "name": "window-close", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "window-close window close browser cancel computer development", "unicode": "&#xf410;", "name": "window-close", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "window-maximize window maximize browser computer development expand", "unicode": "&#xf2d0;", "name": "window-maximize", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "window-maximize window maximize browser computer development expand", "unicode": "&#xf2d0;", "name": "window-maximize", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "window-minimize window minimize browser collapse computer development", "unicode": "&#xf2d1;", "name": "window-minimize", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "window-minimize window minimize browser collapse computer development", "unicode": "&#xf2d1;", "name": "window-minimize", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "window-restore window restore browser computer development", "unicode": "&#xf2d2;", "name": "window-restore", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "window-restore window restore browser computer development", "unicode": "&#xf2d2;", "name": "window-restore", "is_divi_icon": false, "styles": ["line", "fa"], "font_weight": 400}, {"search_terms": "windows microsoft operating system os", "unicode": "&#xf17a;", "name": "windows", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wine-bottle wine bottle alcohol beverage cabernet drink glass grapes merlot sauvignon", "unicode": "&#xf72f;", "name": "wine-bottle", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wine-glass wine glass alcohol beverage cabernet drink grapes merlot sauvignon", "unicode": "&#xf4e3;", "name": "wine-glass", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wine-glass-alt alternate wine glas alcohol beverage cabernet drink grapes merlot sauvignon", "unicode": "&#xf5ce;", "name": "wine-glass-alt", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wix", "unicode": "&#xf5cf;", "name": "wix", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wizards-of-the-coast wizards of the coast dungeons  dragons dd dnd fantasy game gaming tabletop", "unicode": "&#xf730;", "name": "wizards-of-the-coast", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wodu", "unicode": "&#xe088;", "name": "wodu", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wolf-pack-battalionwolf pack battalion", "unicode": "&#xf514;", "name": "wolf-pack-battalion", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "won-sign won sign currency krw money", "unicode": "&#xf159;", "name": "won-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "wordpress logo", "unicode": "&#xf19a;", "name": "wordpress", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wordpress-simplewordpress simple", "unicode": "&#xf411;", "name": "wordpress-simple", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wpbeginner", "unicode": "&#xf297;", "name": "wpbeginner", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wpexplorer", "unicode": "&#xf2de;", "name": "wpexplorer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wpforms", "unicode": "&#xf298;", "name": "wpforms", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wpressr rendact", "unicode": "&#xf3e4;", "name": "wpressr", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "wrench construction fix mechanic plumbing settings spanner tool update", "unicode": "&#xf0ad;", "name": "wrench", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "x-ray health medical radiological images radiology skeleton", "unicode": "&#xf497;", "name": "x-ray", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "xbox", "unicode": "&#xf412;", "name": "xbox", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "xing", "unicode": "&#xf168;", "name": "xing", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "xing-squarexing square", "unicode": "&#xf169;", "name": "xing-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "y-combinatory combinator", "unicode": "&#xf23b;", "name": "y-combinator", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yahoo logo", "unicode": "&#xf19e;", "name": "yahoo", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yammer", "unicode": "&#xf840;", "name": "yammer", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yandex", "unicode": "&#xf413;", "name": "yandex", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yandex-internationalyandex international", "unicode": "&#xf414;", "name": "yandex-international", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yarn", "unicode": "&#xf7e3;", "name": "yarn", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yelp", "unicode": "&#xf1e9;", "name": "yelp", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "yen-sign yen sign currency jpy money", "unicode": "&#xf157;", "name": "yen-sign", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "yin-yang yin yang daoism opposites taoism", "unicode": "&#xf6ad;", "name": "yin-yang", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 900}, {"search_terms": "yoast", "unicode": "&#xf2b1;", "name": "yoast", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "youtube film video youtubeplay youtubesquare", "unicode": "&#xf167;", "name": "youtube", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "youtube-squareyoutube square", "unicode": "&#xf431;", "name": "youtube-square", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}, {"search_terms": "zhihu", "unicode": "&#xf63f;", "name": "zhihu", "is_divi_icon": false, "styles": ["solid", "fa"], "font_weight": 400}]