<?php
/**
 * Internationalization
 *
 * @package Divi
 */

return array(
	'Display Conditions'                    => esc_html__( 'Display Conditions', 'et_builder' ),
	'%s Module'                             => esc_html__( '%s Module', 'et_builder' ),
	'Admin Label'                           => esc_html__( 'Admin Label', 'et_builder' ),
	'Is'                                    => esc_html__( 'Is', 'et_builder' ),
	'Is Not'                                => esc_html__( 'Is Not', 'et_builder' ),
	'Is After'                              => esc_html__( 'Is After', 'et_builder' ),
	'Is Before'                             => esc_html__( 'Is Before', 'et_builder' ),
	'Add Condition'                         => esc_html__( 'Add Condition', 'et_builder' ),
	'Enable Condition'                      => esc_html__( 'Enable Condition', 'et_builder' ),
	'Yes'                                   => esc_html__( 'Yes', 'et_builder' ),
	'No'                                    => esc_html__( 'No', 'et_builder' ),
	'Translatable Cache Notice'             => esc_html__( 'If you are using a caching plugin, This page should be excluded from the cache to allow the conditions you have selected to function properly.', 'et_builder' ),
	'Display Only If'                       => esc_html__( 'Display Only If', 'et_builder' ),
	'Dynamic Posts'                         => esc_html__( 'dynamic Posts', 'et_builder' ),
	'Display if any condition is true'      => esc_html__( 'Display if any condition is true', 'et_builder' ),
	'Display if all conditions are true'    => esc_html__( 'Display if all conditions are true', 'et_builder' ),
	'Display Conditions Min Requirements'   => esc_html__( 'This feature requires at least WordPress 5.3. Please upgrade WordPress to the most recent version to ensure an optimal Divi experience.', 'et_builder' ),

	'Author'                                => esc_html__( 'Author', 'et_builder' ),
	'Display Only If Author'                => esc_html__( 'Display Only If Post Author', 'et_builder' ),

	'Browser'                               => esc_html__( 'Browser', 'et_builder' ),
	'Display Only If Browser'               => esc_html__( 'Display Only If Browser', 'et_builder' ),
	'Chromium Browsers (Chrome, Edge, etc)' => esc_html__( 'Chromium Browsers (Chrome, Edge, etc)', 'et_builder' ),

	'Cart Contents'                         => esc_html__( 'Cart Contents', 'et_builder' ),
	"Display Only If User's Cart"           => esc_html__( "Display Only If User's Cart", 'et_builder' ),
	'Has Products'                          => esc_html__( 'Has Products', 'et_builder' ),
	'Is Empty'                              => esc_html__( 'Is Empty', 'et_builder' ),
	'Has a Specific Product'                => esc_html__( 'Has a Specific Product', 'et_builder' ),
	'Does Not Have a Specific Product'      => esc_html__( 'Does Not Have a Specific Product', 'et_builder' ),

	'Post Category'                         => esc_html__( 'Post Category', 'et_builder' ),
	'Display Only If Post Category'         => esc_html__( 'Display Only If Post Category', 'et_builder' ),

	'Date Archive'                          => esc_html__( 'Date Archive', 'et_builder' ),
	'Display Only on Date Archives'         => esc_html__( 'Display Only on Date Archives', 'et_builder' ),

	'Date & Time'                           => esc_html__( 'Date & Time', 'et_builder' ),
	'Display Only If Current Date'          => esc_html__( 'Display Only If Current Date', 'et_builder' ),
	'Is On a Specific Date'                 => esc_html__( 'Is On a Specific Date', 'et_builder' ),
	'Is Not a Specific Date'                => esc_html__( 'Is Not a Specific Date', 'et_builder' ),
	'Is On Specific Day(s) of the Week'     => esc_html__( 'Is On Specific Day(s) of the Week', 'et_builder' ),
	'Is the First Day of the Month'         => esc_html__( 'Is the First Day of the Month', 'et_builder' ),
	'Is the Last Day of the Month'          => esc_html__( 'Is the Last Day of the Month', 'et_builder' ),
	'Hour'                                  => esc_html__( 'Hour', 'et_builder' ),
	'Minute'                                => esc_html__( 'Minute', 'et_builder' ),
	'Monday'                                => esc_html__( 'Monday', 'et_builder' ),
	'Tuesday'                               => esc_html__( 'Tuesday', 'et_builder' ),
	'Wednesday'                             => esc_html__( 'Wednesday', 'et_builder' ),
	'Thursday'                              => esc_html__( 'Thursday', 'et_builder' ),
	'Friday'                                => esc_html__( 'Friday', 'et_builder' ),
	'Saturday'                              => esc_html__( 'Saturday', 'et_builder' ),
	'Sunday'                                => esc_html__( 'Sunday', 'et_builder' ),
	'All Day'                               => esc_html__( 'All Day', 'et_builder' ),
	'From Hour'                             => esc_html__( 'From Hour', 'et_builder' ),
	'From Minute'                           => esc_html__( 'From Minute', 'et_builder' ),
	'Until Hour'                            => esc_html__( 'Until Hour', 'et_builder' ),
	'Until Minute'                          => esc_html__( 'Until Minute', 'et_builder' ),
	'Repeat'                                => esc_html__( 'Repeat', 'et_builder' ),
	'Repeat Frequency'                      => esc_html__( 'Repeat Frequency', 'et_builder' ),
	'Repeat End'                            => esc_html__( 'Repeat End', 'et_builder' ),
	'Repeat Times'                          => esc_html__( 'Repeat Times', 'et_builder' ),
	'Never'                                 => esc_html__( 'Never', 'et_builder' ),
	'Until Date'                            => esc_html__( 'Until Date', 'et_builder' ),
	'After Number of Times'                 => esc_html__( 'After Number of Times', 'et_builder' ),
	'Weekly'                                => esc_html__( 'Weekly', 'et_builder' ),
	'First Instance of Every Month'         => esc_html__( 'First Instance of Every Month', 'et_builder' ),
	'Last Instance of Every Month'          => esc_html__( 'Last Instance of Every Month', 'et_builder' ),
	'Every Other'                           => esc_html__( 'Every Other', 'et_builder' ),
	'Monthly'                               => esc_html__( 'Monthly', 'et_builder' ),
	'Annually'                              => esc_html__( 'Annually', 'et_builder' ),

	'Logged In Status'                      => esc_html__( 'Logged In Status', 'et_builder' ),
	'User is Logged In'                     => esc_html__( 'User is Logged In', 'et_builder' ),
	'User is Logged Out'                    => esc_html__( 'User is Logged Out', 'et_builder' ),

	'Operating System'                      => esc_html__( 'Operating System', 'et_builder' ),
	'Display Only If Operating System'      => esc_html__( 'Display Only If Operating System', 'et_builder' ),

	'Page Visit'                            => esc_html__( 'Page Visit', 'et_builder' ),
	'Display Only If User'                  => esc_html__( 'Display Only If User', 'et_builder' ),
	'Has Visited a Specific Page'           => esc_html__( 'Has Visited a Specific Page', 'et_builder' ),
	'Has Not Visited a Specific Page'       => esc_html__( 'Has Not Visited a Specific Page', 'et_builder' ),

	'User Role'                             => esc_html__( 'User Role', 'et_builder' ),
	'Display Only If User Role'             => esc_html__( 'Display Only If User Role', 'et_builder' ),
	'Admin'                                 => esc_html__( 'Admin', 'et_builder' ),
	'Contributor'                           => esc_html__( 'Contributor', 'et_builder' ),
	'Customer'                              => esc_html__( 'Customer', 'et_builder' ),
	'Editor'                                => esc_html__( 'Editor', 'et_builder' ),
	'Subscriber'                            => esc_html__( 'Subscriber', 'et_builder' ),

	'Post Type'                             => esc_html__( 'Post Type', 'et_builder' ),
	'Display Only If Post Type'             => esc_html__( 'Display Only If Post Type', 'et_builder' ),

	'Post Tag'                              => esc_html__( 'Post Tag', 'et_builder' ),
	'Display Only If Post Tag'              => esc_html__( 'Display Only If Post Tag', 'et_builder' ),

	'Product Purchase'                      => esc_html__( 'Product Purchase', 'et_builder' ),
	'Has Bought a Product'                  => esc_html__( 'Has Bought a Product', 'et_builder' ),
	'Has Not Bought a Product'              => esc_html__( 'Has Not Bought a Product', 'et_builder' ),
	'Has Bought a Specific Product'         => esc_html__( 'Has Bought a Specific Product', 'et_builder' ),
	'Has Not Bought a Specific Product'     => esc_html__( 'Has Not Bought a Specific Product', 'et_builder' ),

	'Search Results'                        => esc_html__( 'Search Results', 'et_builder' ),
	'Display Only on Search Results for'    => esc_html__( 'Display Only on Search Results for', 'et_builder' ),
	'Specific Search Queries'               => esc_html__( 'Specific Search Queries', 'et_builder' ),
	'Excluded Search Queries'               => esc_html__( 'Excluded Search Queries', 'et_builder' ),
	'Search queries separated by commas'    => esc_html__( 'Search queries separated by commas', 'et_builder' ),

	'Post Visit'                            => esc_html__( 'Post Visit', 'et_builder' ),
	'Has Visited a Specific Post'           => esc_html__( 'Has Visited a Specific Post', 'et_builder' ),
	'Has Not Visited a Specific Post'       => esc_html__( 'Has Not Visited a Specific Post', 'et_builder' ),

	'Cookie'                                => esc_html__( 'Cookie', 'et_builder' ),
	'Cookie Exists'                         => esc_html__( 'Cookie Exists', 'et_builder' ),
	'Cookie Does Not Exist'                 => esc_html__( 'Cookie Does Not Exist', 'et_builder' ),
	'Cookie Value Equals'                   => esc_html__( 'Cookie Value Equals', 'et_builder' ),
	'Cookie Value Does Not Equal'           => esc_html__( 'Cookie Value Does Not Equal', 'et_builder' ),
	'Cookie Name'                           => esc_html__( 'Cookie Name', 'et_builder' ),
	'Cookie Value'                          => esc_html__( 'Cookie Value', 'et_builder' ),

	'Category Page'                         => esc_html__( 'Category Page', 'et_builder' ),
	'Display Only If Category Page'         => esc_html__( 'Display Only If Category Page', 'et_builder' ),

	'Tag Page'                              => esc_html__( 'Tag Page', 'et_builder' ),
	'Display Only If Tag Page'              => esc_html__( 'Display Only If Tag Page', 'et_builder' ),

	'Number of Views'                       => esc_html__( 'Number of Views', 'et_builder' ),
	'Only Display This Many Times'          => esc_html__( 'Only Display This Many Times', 'et_builder' ),
	'Display again after'                   => esc_html__( 'Display again after', 'et_builder' ),
	'Reset After Duration'                  => esc_html__( 'Reset After Duration', 'et_builder' ),
	'Days'                                  => esc_html__( 'Days', 'et_builder' ),
	'Minutes'                               => esc_html__( 'Minutes', 'et_builder' ),
	'Hours'                                 => esc_html__( 'Hours', 'et_builder' ),

	'Custom Field'                          => esc_html__( 'Custom Field', 'et_builder' ),
	'Manual Custom Field Name'              => esc_html__( 'Manual Custom Field Name', 'et_builder' ),
	'Manual Custom Field Value'             => esc_html__( 'Manual Custom Field Value', 'et_builder' ),
	'Custom Field Name'                     => esc_html__( 'Custom Field Name', 'et_builder' ),
	'Contains'                              => esc_html__( 'Contains', 'et_builder' ),
	'Does Not Contain'                      => esc_html__( 'Does Not Contain', 'et_builder' ),
	'Is Any Value'                          => esc_html__( 'Is Any Value', 'et_builder' ),
	'Has No Value'                          => esc_html__( 'Has No Value', 'et_builder' ),
	'Is Greater Than (numeric values only)' => esc_html__( 'Is Greater Than (numeric values only)', 'et_builder' ),
	'Is Less Than (numeric values only)'    => esc_html__( 'Is Less Than (numeric values only)', 'et_builder' ),
	'Custom Field Value'                    => esc_html__( 'Custom Field Value', 'et_builder' ),

	'A Specific URL Parameter'              => esc_html__( 'A Specific URL Parameter', 'et_builder' ),
	'Any URL Parameter'                     => esc_html__( 'Any URL Parameter', 'et_builder' ),
	'Exist'                                 => esc_html__( 'Exist', 'et_builder' ),
	'Does not Exist'                        => esc_html__( 'Does not Exist', 'et_builder' ),
	'Equals'                                => esc_html__( 'Equals', 'et_builder' ),
	'Does not Equal'                        => esc_html__( 'Does not Equal', 'et_builder' ),
	'Contains'                              => esc_html__( 'Contains', 'et_builder' ),
	'Does not Contain'                      => esc_html__( 'Does not Contain', 'et_builder' ),
	'URL Parameter Name'                    => esc_html__( 'URL Parameter Name', 'et_builder' ),
	'URL Parameter Value'                   => esc_html__( 'URL Parameter Value', 'et_builder' ),
	'URL Parameter'                         => esc_html__( 'URL Parameter', 'et_builder' ),
	'Any'                                   => esc_html__( 'Any', 'et_builder' ),

	'Product Stock'                         => esc_html__( 'Product Stock', 'et_builder' ),
	'Display Only If a Specific Product'    => esc_html__( 'Display Only If a Specific Product', 'et_builder' ),
	'Is in stock'                           => esc_html__( 'Is in stock', 'et_builder' ),
	'Is out of stock'                       => esc_html__( 'Is out of stock', 'et_builder' ),
	'Only display if'                       => esc_html__( 'Only display if', 'et_builder' ),
	'products are in stock'                 => esc_html__( 'products are in stock', 'et_builder' ),
	'products are out of stock'             => esc_html__( 'products are out of stock', 'et_builder' ),
	'product is in stock'                   => esc_html__( 'product is in stock', 'et_builder' ),
	'product is out of stock'               => esc_html__( 'product is out of stock', 'et_builder' ),

	// Logged In Status Tooltip.
	'Only display to logged in visitors'    => esc_html__( 'Only display to logged in visitors', 'et_builder' ),
	'Only display to logged out visitors'   => esc_html__( 'Only display to logged out visitors', 'et_builder' ),

	// Post Type and Other Common Tooltips.
	'Only display on'                       => esc_html__( 'Only display on', 'et_builder' ),
	"Don't display on"                      => esc_html__( "Don't display on", 'et_builder' ),

	// Author Tooltip.
	'Only display on posts authored by'     => esc_html__( 'Only display on posts authored by', 'et_builder' ),
	"Don't display on posts authored by"    => esc_html__( "Don't display on posts authored by", 'et_builder' ),

	// User Role and Other Common Tooltips.
	'Only display to'                       => esc_html__( 'Only display to', 'et_builder' ),
	"Don't display to"                      => esc_html__( "Don't display to", 'et_builder' ),

	// Date & Time Tooltip.
	'Only display after'                    => esc_html__( 'Only display after', 'et_builder' ),
	'Only display before'                   => esc_html__( 'Only display before', 'et_builder' ),
	'Only display on first day of month'    => esc_html__( 'Only display on first day of the month', 'et_builder' ),
	'Only display on last day of month'     => esc_html__( 'Only display on last day of the month', 'et_builder' ),

	// Category Page, Tag Page, Page Visit Tooltip.
	'Pages'                                 => esc_html__( 'Pages', 'et_builder' ),

	// Cart Content Tooltip.
	'Cart Contents Has Products'            => esc_html__( 'Only display to users with Products in their cart', 'et_builder' ),
	'Cart Contents Is Empty'                => esc_html__( 'Only display to users with an empty cart', 'et_builder' ),
	'Only display to users with'            => esc_html__( 'Only display to users with', 'et_builder' ),
	'in their cart'                         => esc_html__( 'in their cart', 'et_builder' ),
	'Only display to users without'         => esc_html__( 'Only display to users without', 'et_builder' ),

	// Cookie Tooltip.
	'cookie'                                => esc_html__( 'cookie', 'et_builder' ),
	'cookie with'                           => esc_html__( 'cookie with', 'et_builder' ),
	'value'                                 => esc_html__( 'value', 'et_builder' ),
	'Only display to users who have'        => esc_html__( 'Only display to users who have', 'et_builder' ),
	"Don't display to users who have"       => esc_html__( "Don't display to users who have", 'et_builder' ),

	// Date Archive Tooltip.
	'Only display on date archives after'   => esc_html__( 'Only display on date archives after', 'et_builder' ),
	'Only display on date archives before'  => esc_html__( 'Only display on date archives before', 'et_builder' ),

	// Number of Views Tooltip.
	'Only display'                          => esc_html__( 'Only display', 'et_builder' ),
	', Reset after'                         => esc_html__( ', Reset after', 'et_builder' ),
	'times'                                 => esc_html__( 'times', 'et_builder' ),

	// Page Visit, Post Visit Tooltip.
	'Only display to users visited'         => esc_html__( 'Only display to users who have visited', 'et_builder' ),
	"Don't display to users visited"        => esc_html__( "Don't display to users who have visited", 'et_builder' ),
	'Posts'                                 => esc_html__( 'Posts', 'et_builder' ),

	// Product Purchase Tooltip.
	'Only display to users bought product'  => esc_html__( 'Only display to users who have bought a product', 'et_builder' ),
	"Don't display to users bought product" => esc_html__( "Don't display to users who have bought a product", 'et_builder' ),
	'Only Display to users who bought'      => esc_html__( 'Only Display to users who have bought', 'et_builder' ),
	"Don't display to users who bought"     => esc_html__( "Don't display to users who have bought", 'et_builder' ),

	// Search Results Tooltip.
	'Only display on search results for'    => esc_html__( 'Only display on search results for', 'et_builder' ),
	"Don't display on search results for"   => esc_html__( "Don't display on search results for", 'et_builder' ),

	// Custom Field Tooltip.
	'Only display on posts where'           => esc_html__( 'Only display on posts where', 'et_builder' ),
	'custom field'                          => esc_html__( 'custom field', 'et_builder' ),
	'is equal to'                           => esc_html__( 'is equal to', 'et_builder' ),
	'is not equal to'                       => esc_html__( 'is not equal to', 'et_builder' ),
	'contains'                              => esc_html__( 'custom field', 'et_builder' ),
	'does not contain'                      => esc_html__( 'does not contain', 'et_builder' ),
	'is any value'                          => esc_html__( 'is any value', 'et_builder' ),
	'has no value'                          => esc_html__( 'has no value', 'et_builder' ),
	'is greater than'                       => esc_html__( 'is greater than', 'et_builder' ),
	'is less than'                          => esc_html__( 'is less than', 'et_builder' ),
);
