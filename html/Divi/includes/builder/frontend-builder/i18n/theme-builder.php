<?php
/**
 * Internationalization.
 *
 * @package Divi
 */

return array(
	'A Post Content module is required in order to add unique content within the Theme Builder template.' => esc_html__( 'A Post Content module is required in order to add unique content within the Theme Builder template. Once a Post Content module has been added to the template, you will be able to add unique post content within that area for each post using the template.', 'et_builder' ),
	'Add Custom Body'                                      => esc_html__( 'Add Custom Body', 'et_builder' ),
	'Add Custom Footer'                                    => esc_html__( 'Add Custom Footer', 'et_builder' ),
	'Add Custom Header'                                    => esc_html__( 'Add Custom Header', 'et_builder' ),
	'Add From Library'                                     => esc_html__( 'Add From Library', 'et_builder' ),
	'Add Global Body'                                      => esc_html__( 'Add Global Body', 'et_builder' ),
	'Add Global Footer'                                    => esc_html__( 'Add Global Footer', 'et_builder' ),
	'Add Global Header'                                    => esc_html__( 'Add Global Header', 'et_builder' ),
	'Add New Template'                                     => esc_html__( 'Add New Template', 'et_builder' ),
	'Added template'                                       => esc_html__( 'Added template', 'et_builder' ),
	// Translators: %1$s: Number of extra template conditions.
	'And %1$s More'                                        => esc_html__( 'And %1$s More', 'et_builder' ),
	'Assigned layout to template'                          => esc_html__( 'Assigned layout to template', 'et_builder' ),
	'Backup Exists'                                        => esc_html__( 'Backup Exists', 'et_builder' ),
	'Backup Exists Content'                                => esc_html__( 'A backup exists for the Theme Builder that is newer than the version you are currently viewing. This backup was captured during your previous editing session, but you never saved it. Would you like to restore this backup and continue editing where you left off?', 'et_builder' ),
	'Build Custom Body'                                    => esc_html__( 'Build Custom Body', 'et_builder' ),
	'Build Custom Footer'                                  => esc_html__( 'Build Custom Footer', 'et_builder' ),
	'Build Custom Header'                                  => esc_html__( 'Build Custom Header', 'et_builder' ),
	'Build Global Body'                                    => esc_html__( 'Build Global Body', 'et_builder' ),
	'Build Global Footer'                                  => esc_html__( 'Build Global Footer', 'et_builder' ),
	'Build Global Header'                                  => esc_html__( 'Build Global Header', 'et_builder' ),
	'Build New Template'                                   => esc_html__( 'Build New Template', 'et_builder' ),
	'Copy From Another Template'                           => esc_html__( 'Copy From Template', 'et_builder' ),
	'Duplicated layout'                                    => esc_html__( 'Duplicated layout', 'et_builder' ),
	'Duplicated template'                                  => esc_html__( 'Duplicated template', 'et_builder' ),
	'Create Template'                                      => esc_html__( 'Create Template', 'et_builder' ),
	'Custom Body'                                          => esc_html__( 'Custom Body', 'et_builder' ),
	'Custom Footer'                                        => esc_html__( 'Custom Footer', 'et_builder' ),
	'Custom Header'                                        => esc_html__( 'Custom Header', 'et_builder' ),
	'Default Website Template'                             => esc_html__( 'Default Website Template', 'et_builder' ),
	'Delete Global Layout'                                 => esc_html__( 'Delete Global Layout', 'et_builder' ),
	'Delete Template'                                      => esc_html__( 'Delete Template', 'et_builder' ),
	'Deleted template'                                     => esc_html__( 'Deleted template', 'et_builder' ),
	'Disable Template'                                     => esc_html__( 'Disable Template', 'et_builder' ),
	'Disabled template'                                    => esc_html__( 'Disabled template', 'et_builder' ),
	'Divi Theme Builder'                                   => esc_html__( 'Divi Theme Builder', 'et_builder' ),
	'Divi Theme Builder Template'                          => esc_html__( 'Divi Theme Builder Template', 'et_builder' ),
	'Divi Theme Builder Templates'                         => esc_html__( 'Divi Theme Builder Templates', 'et_builder' ),
	'Theme Builder Library Templates'                      => esc_html__( 'Theme Builder Library Templates', 'et_builder' ),
	'Don\'t Restore'                                       => esc_html__( 'Don\'t Restore', 'et_builder' ),
	'Duplicate Template'                                   => esc_html__( 'Duplicate Template', 'et_builder' ),
	// Translators: %1$s: layout name.
	'Edit %1$s Layout'                                     => esc_html__( 'Edit %1$s Layout', 'et_builder' ),
	'Edit Template Layout'                                 => esc_html__( 'Edit Template Layout', 'et_builder' ),
	'Enable Template'                                      => esc_html__( 'Enable Template', 'et_builder' ),
	'Enabled template'                                     => esc_html__( 'Enabled template', 'et_builder' ),
	'Exclude From'                                         => esc_html__( 'Exclude From', 'et_builder' ),
	// Translators: %1$s: plural post type e.g. Posts.
	'Exclude From: %1$s'                                   => esc_html__( 'Exclude From: %1$s', 'et_builder' ),
	'Failed to duplicate your layout. Please try again later.' => esc_html__( 'Failed to duplicate your layout. Please try again later.', 'et_builder' ),
	'Failed to create your layout. Please try again later.' => esc_html__( 'Failed to create your layout. Please try again later.', 'et_builder' ),
	'Failed to load the Theme Builder'                     => esc_html__( 'Failed to load the Theme Builder.', 'et_builder' ),
	'Global Body'                                          => esc_html__( 'Global Body', 'et_builder' ),
	'Global Footer'                                        => esc_html__( 'Global Footer', 'et_builder' ),
	'Global Header'                                        => esc_html__( 'Global Header', 'et_builder' ),
	'Hide On Front End'                                    => esc_html__( 'Hide On Front End', 'et_builder' ),
	'Imported templates'                                   => esc_html__( 'Imported templates', 'et_builder' ),
	'Import them as static layouts from the website they were exported from.' => esc_html__( 'Import them as static layouts from the website they were exported from. This may take several minutes depending on the number of references being imported.', 'et_builder' ),
	'Loaded Theme Builder'                                 => esc_html__( 'Loaded Theme Builder', 'et_builder' ),
	'Manage Template Assignments'                          => esc_html__( 'Manage Template Assignments', 'et_builder' ),
	'Note: this action may take several minutes depending on the number of templates using this layout.' => esc_html__( 'Note: this action may take several minutes depending on the number of templates using this layout.', 'et_builder' ),
	'Oops, it looks like the current Theme Builder Template body layout is disabled.' => esc_html__( 'Oops, it looks like the current Theme Builder Template body layout is disabled.', 'et_builder' ),
	'Relink them to this website\'s global Header, Body, and/or Footer.' => esc_html__( 'Relink them to this website\'s global Header, Body, and/or Footer.', 'et_builder' ),
	'Removed layout from template'                         => esc_html__( 'Removed layout from template', 'et_builder' ),
	'Rename Template'                                      => esc_html__( 'Rename Template', 'et_builder' ),
	'Renamed template'                                     => esc_html__( 'Renamed template', 'et_builder' ),
	'Reset All Templates'                                  => esc_html__( 'Reset All Templates', 'et_builder' ),
	'Reset Template Assignments'                           => esc_html__( 'Reset Template Assignments', 'et_builder' ),
	'Reset Use On Filters'                                 => esc_html__( 'Reset Use On Filters', 'et_builder' ),
	'Reset Exclude From Filters'                           => esc_html__( 'Reset Exclude From Filters', 'et_builder' ),
	'Restore'                                              => esc_html__( 'Restore', 'et_builder' ),
	'Restored draft'                                       => esc_html__( 'Restored draft', 'et_builder' ),
	// Translators: %1$s: plural post type e.g. Posts.
	'Search %1$s'                                          => esc_html__( 'Search %1$s', 'et_builder' ),
	'Show On Front End'                                    => esc_html__( 'Show On Front End', 'et_builder' ),
	'Template'                                             => esc_html__( 'Template', 'et_builder' ),
	'Template Settings'                                    => esc_html__( 'Template Settings', 'et_builder' ),
	'The body layout of a template should not be disabled if you wish to display and edit the content of individual posts using this layout.' => esc_html__( 'The body layout of a template should not be disabled if you wish to display and edit the content of individual posts using this layout.', 'et_builder' ),
	'This import contains references to global layouts.'   => esc_html__( 'This import contains references to the global Header, Body, and/or Footer from the website they were exported from. How would you like to import these references?', 'et_builder' ),
	'This item is assigned to another template.'           => esc_html__( 'This item is assigned to another template. Saving will override and remove its current assignment.', 'et_builder' ),
	'This post has been assigned a template using the Theme Builder, however, the template being used does not contain a Post Content module.' => esc_html__( 'This post has been assigned a template using the Theme Builder, however, the template being used does not contain a Post Content module.', 'et_builder' ),
	'This will reset all Theme Builder settings and delete all templates.' => esc_html__( 'This will reset all Theme Builder settings and delete all templates. Are you sure you want to do this?', 'et_builder' ),
	'Toggled layout visibility'                            => esc_html__( 'Toggled layout visibility', 'et_builder' ),
	'Unassigned'                                           => esc_html__( 'Unassigned', 'et_builder' ),
	'Unsaved Changes Warning'                              => esc_html__( 'Unsaved changes will be lost if you leave the Theme Builder at this time.', 'et_builder' ),
	'Updated template settings'                            => esc_html__( 'Updated template settings', 'et_builder' ),
	'Updated templates order'                              => esc_html__( 'Updated templates order', 'et_builder' ),
	'Use Global Body'                                      => esc_html__( 'Use Global Body', 'et_builder' ),
	'Use Global Footer'                                    => esc_html__( 'Use Global Footer', 'et_builder' ),
	'Use Global Header'                                    => esc_html__( 'Use Global Header', 'et_builder' ),
	'Use On'                                               => esc_html__( 'Use On', 'et_builder' ),
	'You are about to delete a global layout.'             => esc_html__( 'You are about to delete a global layout. This will disconnect all templates that use this global layout and duplicate it in place. Are you sure you wish to continue?', 'et_builder' ),
	'You Have Unsaved Changes'                             => esc_html__( 'You Have Unsaved Changes', 'et_builder' ),
	'Your Dynamic Post Title Will Display Here'            => esc_html__( 'Your Dynamic Post Title Will Display Here', 'et_builder' ),
	'Save To Divi Cloud'                                   => esc_html__( 'Save To Divi Cloud', 'et_builder' ),
	'Add To Category'                                      => esc_html__( 'Add To Category', 'et_builder' ),
	'Add To Tags'                                          => esc_html__( 'Add To Tags', 'et_builder' ),
	'Save Template To Library'                             => esc_html__( 'Save Template To Library', 'et_builder' ),
	'Save Theme Builder Template'                          => esc_html__( 'Save Theme Builder Template', 'et_builder' ),
	'Save this template to the Divi Library for later use' => esc_html__( 'Save this template to the Divi Library for later use', 'et_builder' ),
	'saveToLibraryModalPreferences'                        => array(
		'template_name'     => array(
			'label'       => esc_html__( 'Template Name', 'et_builder' ),
			'placeholder' => esc_html__( 'Divi Theme Builder Template', 'et_builder' ),
		),
		'set_name'          => array(
			'label'       => esc_html__( 'Set Name', 'et_builder' ),
			'placeholder' => esc_html__( 'Divi Theme Builder Set', 'et_builder' ),
		),
		'new_category_name' => array(
			'placeholder' => esc_html__( 'Create New Category', 'et_builder' ),
		),
		'new_tag_name'      => array(
			'placeholder' => esc_html__( 'Create New Tag', 'et_builder' ),
		),
	),
	'Save Theme Builder Set'                               => esc_html__( 'Save Theme Builder Set', 'et_builder' ),
	'Save this set of templates to the Divi Library for later use' => esc_html__( 'Save this set of templates to the Divi Library for later use', 'et_builder' ),
	'Select Templates'                                     => esc_html__( 'Select Templates', 'et_builder' ),
	'Include All Templates'                                => esc_html__( 'Include All Templates', 'et_builder' ),
	'Failed to load your existing items.'                  => esc_html__( 'Failed to load your existing items. Please try again later.', 'et_builder' ),
	'Add From Sets Library'                                => esc_html__( 'Add From Sets Library', 'et_builder' ),
	'Set'                                                  => esc_html__( 'Set', 'et_builder' ),
	'Template'                                             => esc_html__( 'Template', 'et_builder' ),
	'Edit With Theme Builder'                              => esc_html__( 'Edit With Theme Builder', 'et_builder' ),
	'Load From Library'                                    => esc_html__( 'Load From Library', 'et_builder' ),
	'Insert Theme Builder Set'                             => esc_html__( 'Insert Theme Builder Set', 'et_builder' ),
	'Insert Theme Builder Template'                        => esc_html__( 'Insert Theme Builder Template', 'et_builder' ),
	'Set Details'                                          => esc_html__( 'Set Details', 'et_builder' ),
	'Template Details'                                     => esc_html__( 'Template Details', 'et_builder' ),
	'Override Existing Assignments'                        => esc_html__( 'Override Existing Assignments', 'et_builder' ),
	'Download Backup Before Importing'                     => esc_html__( 'Download Backup Before Importing', 'et_builder' ),
	'Import Divi Builder Templates'                        => esc_html__( 'Import Divi Builder Templates', 'et_builder' ),
	'Import Presets'                                       => esc_html__( 'Import Presets', 'et_builder' ),
	'Import Templates'                                     => esc_html__( 'Import Templates', 'et_builder' ),
	'Export Templates'                                     => esc_html__( 'Export Templates', 'et_builder' ),
	'Options'                                              => esc_html__( 'Options', 'et_builder' ),
	'Override Existing Default Website Template'           => esc_html__( 'Override Existing Default Website Template', 'et_builder' ),
	'Loaded Template'                                      => esc_html__( 'Loaded Template', 'et_builder' ),
	'Loaded Preset'                                        => esc_html__( 'Loaded Preset', 'et_builder' ),
	'Help'                                                 => esc_html__( 'Help', 'et_builder' ),
	'Load Template'                                        => esc_html__( 'Load Template', 'et_builder' ),
	'How do you want to load this Template?'               => esc_html__( 'How do you want to load this Template?', 'et_builder' ),
	'Override Active Global Templates'                     => esc_html__( 'Override Active Global Templates', 'et_builder' ),
	'Use With Global Templates From This Website'          => esc_html__( 'Use With Global Templates From This Website', 'et_builder' ),
	'Library Item Error'                                   => esc_html__( 'Library Item Error', 'et_builder' ),
	'Library item does not exist. Click close to close this browser window.' => esc_html__( 'Library item does not exist. Click close to close this browser window.', 'et_builder' ),
	'Save To Divi Library'                                 => esc_html__( 'Save To Divi Library', 'et_builder' ),
	'Log In To Divi Cloud'                                 => esc_html__( 'Log In To Divi Cloud', 'et_builder' ),
	'$cloudLoginNotification'                              => esc_html__( 'Sign in to your Elegant Themes account using the pop-up to activate Divi Cloud.', 'et_builder' ),
	'Cancel'                                               => esc_html__( 'Cancel', 'et_builder' ),
	'Save To Divi Cloud'                                   => esc_html__( 'Save To Divi Cloud', 'et_builder' ),
	'$cloudBuyNotification'                                => esc_html__( 'Complete your purchase using the pop-up to upgrade Divi Cloud.', 'et_builder' ),
	'Upgrade Your Divi Cloud'                              => esc_html__( 'Upgrade Your Divi Cloud', 'et_builder' ),
	'Free Divi Cloud Limit Reached'                        => esc_html__( 'Free Divi Cloud Limit Reached', 'et_builder' ),
	'Get Unlimited Divi Cloud Storage'                     => esc_html__( 'Get Unlimited Divi Cloud Storage', 'et_builder' ),
	'$cloudUpgradeMessage'                                 => esc_html__( 'All Divi users can store up to %s items to their Divi Cloud for free. To save more items to your cloud and unlock unlimited storage, upgrade your Divi Cloud membership today. If you build Divi websites for a living, Divi Cloud is an awesome add-on and an amazing time saver. You are going to love it!', 'et_builder' ),
	'Saving To Divi Cloud'                                 => esc_html__( 'Saving To Divi Cloud', 'et_builder' ),
	'Return To Theme Builder'                              => esc_html__( 'Return To Theme Builder', 'et_builder' ),
	'My Divi Cloud'                                        => esc_html__( 'My Divi Cloud', 'et_builder' ),
	'My Cloud'                                             => esc_html__( 'My Cloud', 'et_builder' ),
	'Choose Library'                                       => esc_html__( 'Choose Library', 'et_builder' ),
);
