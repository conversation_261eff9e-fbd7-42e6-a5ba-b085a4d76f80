<?php
/**
 * Internationalization.
 *
 * @package Divi
 */

return array(
	'Override Existing Assignments'                     => esc_html__( 'Override Existing Assignments', 'et_builder' ),
	'Import Presets'                                    => esc_html__( 'Import Presets', 'et_builder' ),
	'Download Backup Before Importing'                  => esc_html__( 'Download Backup Before Importing', 'et_builder' ),
	'Export'                                            => esc_html__( 'Export', 'et_builder' ),
	'Export All Templates'                              => esc_html__( 'Export All Templates', 'et_builder' ),
	// Translators: %s: number of remaining minutes.
	'Export estimated time remaining: %smin'            => esc_html__( 'Export estimated time remaining: %smin', 'et_builder' ),
	'Export File Name'                                  => esc_html__( 'Export File Name', 'et_builder' ),
	'Export Options'                                    => esc_html__( 'Export Options', 'et_builder' ),
	// Translators: %s: filename.
	'Export %s'                                         => esc_html__( 'Export %s', 'et_builder' ),
	'Import'                                            => esc_html__( 'Import', 'et_builder' ),
	// Translators: %s: filename.
	'Import %s'                                         => esc_html__( 'Import %s', 'et_builder' ),
	// Translators: %s: number of remaining minutes.
	'Import estimated time remaining: %smin'            => esc_html__( 'Import estimated time remaining: %smin', 'et_builder' ),
	'This file should not be imported in this context.' => esc_html__( 'This file should not be imported in this context.', 'et_builder' ),
	'Override Existing Default Website Template.'       => esc_html__( 'Override Existing Default Website Template.', 'et_builder' ),
	'Portability'                                       => esc_html__( 'Portability', 'et_builder' ),
	'$invalid_file'                                     => esc_html__( 'Invalid file format. You should be uploading a JSON file.', 'et_builder' ),
	'Import To Cloud'                                   => esc_html__( 'Import To Cloud', 'et_builder' ),
	'Import Templates'                                  => esc_html__( 'Import Templates', 'et_builder' ),
	'Export Templates'                                  => esc_html__( 'Export Templates', 'et_builder' ),
	'Import & Export Templates'                         => esc_html__( 'Import & Export Templates', 'et_builder' ),
);
