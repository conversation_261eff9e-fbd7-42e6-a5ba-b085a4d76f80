!function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";(function(e){function t(){return e("#wp-admin-bar-et-disable-visual-builder a").attr("href")}e("body").on("click",".et_builder_increase_memory",(function(){var t=e(this);return e.ajax({type:"POST",dataType:"json",url:et_fb_options.ajaxurl,data:{action:"et_pb_increase_memory_limit",et_admin_load_nonce:et_fb_options.et_admin_load_nonce},success:function(e){_.isUndefined(e.success)?t.addClass("et_builder_modal_action_button_fail").prop("disabled",!0).text(et_fb_options.memory_limit_not_increased):t.addClass("et_builder_modal_action_button_success").text(et_fb_options.memory_limit_increased)}}),!1})),e("body").on("click",".et-builder-timeout .et-core-modal-action",(function(){return location.reload(),!1})),e("body").on("click",".et-builder-timeout .et-core-modal-close, .et-builder-timeout",(function(){return location.assign(t()),!1})),e("body").on("click",".et-theme-builder-no-post-content .et-core-modal-close, .et-theme-builder-no-post-content",(function(n){e(n.target).is(".et-core-modal-action")||(n.preventDefault(),location.assign(t()))}))}).call(this,n(1))},function(e,t){e.exports=jQuery}]);