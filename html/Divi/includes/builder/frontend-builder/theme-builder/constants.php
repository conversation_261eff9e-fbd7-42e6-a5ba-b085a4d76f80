<?php
/**
 * Theme Builder Library constants.
 *
 * @package Builder
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

if ( ! defined( 'ET_THEME_BUILDER_ITEM_SET' ) ) {
	define( 'ET_THEME_BUILDER_ITEM_SET', 'set' );
}

if ( ! defined( 'ET_THEME_BUILDER_ITEM_TEMPLATE' ) ) {
	define( 'ET_THEME_BUILDER_ITEM_TEMPLATE', 'template' );
}

if ( ! defined( 'ET_THEME_BUILDER_EDITOR_STANDARD' ) ) {
	define( 'ET_THEME_BUILDER_EDITOR_STANDARD', 'standard' );
}

if ( ! defined( 'ET_THEME_BUILDER_TAXONOMY_TYPE' ) ) {
	define( 'ET_THEME_BUILDER_TAXONOMY_TYPE', 'et_tb_item_type' );
}
