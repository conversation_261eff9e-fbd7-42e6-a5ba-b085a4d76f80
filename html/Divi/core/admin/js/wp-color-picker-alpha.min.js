/*! wp-color-picker-alpha - v1.1
* https://github.com/23r9i0/wp-color-picker-alpha
* Copyright (c) 2015 Sergio P<PERSON>A. (23r9i0); Licensed GPLv2 */
!function(t){var e="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAIAAAHnlligAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAHJJREFUeNpi+P///4EDBxiAGMgCCCAGFB5AADGCRBgYDh48CCRZIJS9vT2QBAggFBkmBiSAogxFBiCAoHogAKIKAlBUYTELAiAmEtABEECk20G6BOmuIl0CIMBQ/IEMkO0myiSSraaaBhZcbkUOs0HuBwDplz5uFJ3Z4gAAAABJRU5ErkJggg==",i="Default",o="Select default color",r="Clear",a="Clear color",l="Color value",n="Select Color";if("undefined"!=typeof wp&&void 0!==wp.i18n&&void 0!==wp.i18n.__){var s=wp.i18n.__;i=s("Default"),o=s("Select default color"),r=s("Clear"),a=s("Clear color"),l=s("Color value"),n=s("Select Color")}else"undefined"!=typeof wpColorPickerL10n&&void 0!==wpColorPickerL10n.current&&(i=wpColorPickerL10n.defaultString,o=wpColorPickerL10n.defaultAriaLabel,r=wpColorPickerL10n.clear,a=wpColorPickerL10n.clearAriaLabel,l=wpColorPickerL10n.defaultLabel,n=wpColorPickerL10n.pick);Color.fn.toString=function(){if(this._alpha<1)return this.toCSS("rgba",this._alpha).replace(/\s+/g,"");var t=parseInt(this._color,10).toString(16);if(this.error)return"";if(t.length<6)for(var e=6-t.length-1;e>=0;e--)t="0"+t;return"#"+t},t.widget("wp.wpColorPicker",t.wp.wpColorPicker,{_create:function(){if(t.support.iris){var s=this,p=s.element;if(t.extend(s.options,p.data()),"hue"===s.options.type)return s._createHueOnly();s.close=s.close.bind(s),s.initialValue=p.val(),p.addClass("wp-color-picker"),p.parent("label").length||(p.wrap("<label></label>"),s.wrappingLabelText=t('<span class="screen-reader-text"></span>').insertBefore(p).text(l)),s.wrappingLabel=p.parent(),s.wrappingLabel.wrap('<div class="wp-picker-container" />'),s.wrap=s.wrappingLabel.parent(),s.toggler=t('<button type="button" class="button wp-color-result" aria-expanded="false"><span class="wp-color-result-text"></span></button>').insertBefore(s.wrappingLabel).css({backgroundColor:s.initialValue}).attr("title",n).addClass("et-wp-color-result-updated"),"undefined"!=typeof et_pb_color_picker_strings&&s.toggler.attr("data-legacy_title",et_pb_color_picker_strings.legacy_pick).attr("data-current",et_pb_color_picker_strings.legacy_current),s.toggler.find(".wp-color-result-text").text(n),s.pickerContainer=t('<div class="wp-picker-holder" />').insertAfter(s.wrappingLabel),s.button=t('<input type="button" class="button button-small button-clear hidden" />'),s.close_button=t('<button type="button" class="button button-confirm" />').html('<div style="fill: #3EF400; width: 25px; height: 25px; margin-top: -1px;"><svg viewBox="0 0 28 28" preserveAspectRatio="xMidYMid meet" shapeRendering="geometricPrecision"><g><path d="M19.203 9.21a.677.677 0 0 0-.98 0l-5.71 5.9-2.85-2.95a.675.675 0 0 0-.98 0l-1.48 1.523a.737.737 0 0 0 0 1.015l4.82 4.979a.677.677 0 0 0 .98 0l7.68-7.927a.737.737 0 0 0 0-1.015l-1.48-1.525z" fillRule="evenodd" /></g></svg></div>'),s.options.diviColorpicker&&p.after(s.close_button),s.options.defaultColor?s.button.addClass("wp-picker-default").val(i).attr("aria-label",o):s.button.addClass("wp-picker-clear").val(r).attr("aria-label",a),s.wrappingLabel.wrap('<span class="wp-picker-input-wrap hidden" />').after(s.button),s.inputWrapper=p.closest(".wp-picker-input-wrap"),s.toggler.css({height:"24px",margin:"0 6px 6px 0",padding:"0 0 0 30px","font-size":"11px"}),p.iris({target:s.pickerContainer,hide:s.options.hide,width:s.options.width,height:s.options.height,mode:s.options.mode,palettes:s.options.palettes,diviColorpicker:s.options.diviColorpicker,change:function(t,i){s.options.alpha?(s.toggler.css({"background-image":"url("+e+")",position:"relative"}),0==s.toggler.find("span.color-alpha").length&&s.toggler.append('<span class="color-alpha" />'),s.toggler.find("span.color-alpha").css({width:"100%",height:"100%",position:"absolute",top:"0px",left:"0px","border-top-left-radius":"3px","border-bottom-left-radius":"3px",background:i.color.toString()})):s.toggler.css({backgroundColor:i.color.toString()}),"function"==typeof s.options.change&&s.options.change.call(this,t,i)}}),p.val(s.initialValue),s._addListeners(),s.options.hide||s.toggler.trigger("click")}},_addListeners:function(){var e=this;e.wrap.on("click.wpcolorpicker",function(t){return t.stopPropagation(),!1}),e.toggler.on("click",function(){e.toggler.hasClass("wp-picker-open")?e.close():e.open()}),e.element.on("change",function(i){(""===t(this).val()||e.element.hasClass("iris-error"))&&(e.options.alpha?e.toggler.find("span.color-alpha").css("backgroundColor",""):e.toggler.css("backgroundColor",""),"function"==typeof e.options.clear&&e.options.clear.call(this,i))}),e.button.on("click",function(i){t(this).hasClass("wp-picker-clear")?(e.element.val(""),e.options.alpha?e.toggler.find("span.color-alpha").css("backgroundColor",""):e.toggler.css("backgroundColor",""),"function"==typeof e.options.clear&&e.options.clear.call(this,i)):t(this).hasClass("wp-picker-default")&&e.element.val(e.options.defaultColor).trigger("change")}),e.close_button.on("click",function(t){t.preventDefault(),e.close()})},close:function(){this._super();"function"==typeof this.options.onClose&&this.options.onClose.call(this)}}),t.widget("a8c.iris",t.a8c.iris,{_create:function(){if(this._super(),this.options.alpha=this.element.data("alpha")||!1,this.element.is(":input")||(this.options.alpha=!1),void 0!==this.options.alpha&&this.options.alpha){var e=this,i=e.element,o=t('<div class="iris-strip iris-slider iris-alpha-slider"><div class="iris-slider-offset iris-slider-offset-alpha"></div></div>').appendTo(e.picker.find(".iris-picker-inner")),r=o.find(".iris-slider-offset-alpha"),a={aContainer:o,aSlider:r};e.options.defaultWidth=i.width(),(e._color._alpha<1||1!=e._color.toString().indexOf("rgb"))&&i.width(parseInt(e.options.defaultWidth+100)),t.each(a,function(t,i){e.controls[t]=i}),e.controls.square.css({"margin-right":"0px"});var l=e.picker.width()-e.controls.square.width()-20,n=l/6,s=l/2-n;t.each(["aContainer","strip"],function(t,i){e.controls[i].width(s).css({"margin-left":n+"px"})}),e._initControls(),e._change()}},_initControls:function(){if(this._super(),this.options.alpha){var t=this;t.controls.aSlider.slider({orientation:"vertical",min:0,max:100,step:1,value:parseInt(100*t._color._alpha),slide:function(e,i){t._color._alpha=parseFloat(i.value/100),t._change.apply(t,arguments)}})}},_change:function(){this._super();var t=this,i=t.element;if(this.options.alpha){var o=t.controls,r=parseInt(100*t._color._alpha),a=t._color.toRgb(),l=["rgb("+a.r+","+a.g+","+a.b+") 0%","rgba("+a.r+","+a.g+","+a.b+", 0) 100%"],n=t.options.defaultWidth,s=t.picker.closest(".wp-picker-container").find(".wp-color-result");if(o.aContainer.css({background:"linear-gradient(to bottom, "+l.join(", ")+"), url("+e+")"}),s.hasClass("wp-picker-open"))if(o.aSlider.slider("value",r),t._color._alpha<1){var p=o.strip.attr("style").replace(/rgba\(([0-9]+,)(\s+)?([0-9]+,)(\s+)?([0-9]+)(,(\s+)?[0-9\.]+)\)/g,"rgb($1$3$5)");o.strip.attr("style",p),i.width(parseInt(n+100))}else i.width(n)}(i.data("reset-alpha")||!1)&&t.picker.find(".iris-palette-container").on("click.palette",".iris-palette",function(){t._color._alpha=1,t.active="external",t._change()})},_addInputListeners:function(t){var e=this,i=function(i){var o=new Color(t.val()),r=t.val();t.removeClass("iris-error"),o.error?""!==r&&t.addClass("iris-error"):o.toString()!==e._color.toString()&&("keyup"===i.type&&r.match(/^[0-9a-fA-F]{3}$/)||e._setOption("color",o.toString()))};t.on("change",i).on("keyup",e._debounce(i,700)),e.options.hide&&t.one("focus",function(){e.show()})},_dimensions:function(e){var i,o,r,a=this.options,l=this.controls.square,n=this.picker.find(".iris-strip"),s="77.5%",p="12%",c=a.border?a.width-20:a.width,h=Array.isArray(a.palettes)?a.palettes.length:this._palettes.length;if(e&&(l.css("width",""),n.css("width",""),this.picker.css({width:"",height:""})),s=c*(parseFloat(s)/100),p=c*(parseFloat(p)/100),i=a.border?s+20:s,a.diviColorpicker?(l.width(a.width).height(a.height),i=a.height):l.width(s).height(s),n.height(s).width(p),this.picker.css({width:"number"==typeof a.width?a.width+"px":a.width,height:"number"==typeof i?i+"px":i}),!a.palettes)return this.picker.css("paddingBottom","");r=(s-(h-1)*(o=2*s/100))/h,this.picker.find(".iris-palette").each(function(e){var i=0===e?0:o;t(this).css({width:r+"px",height:r+"px",marginLeft:i+"px"})}),this.picker.css("paddingBottom",r+o+"px"),n.height(r+o+s)}})}(jQuery),jQuery(function(t){t(".color-picker").wpColorPicker()});