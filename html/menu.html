<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Menu</title>

    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- jQuery 3.7.1 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
            overflow-x: hidden;
        }

        .mobile-menu {
            width: 393px;
            height: 852px;
            background-color: #fff;
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .menu-header {
            background-color: #1e3a5f;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
        }

        .menu-header .time {
            font-weight: 600;
            font-size: 16px;
        }

        .menu-header .status-icons {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .menu-header .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .menu-container {
            position: relative;
            height: calc(100% - 60px);
            overflow: hidden;
        }

        .menu-level {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform;
        }

        /* Initial positions */
        .menu-level {
            transform: translateX(100%);
        }

        .menu-level.active {
            transform: translateX(0);
        }

        /* Animation states */
        .menu-level.slide-out-left {
            transform: translateX(-100%);
        }

        .menu-level.slide-out-right {
            transform: translateX(100%);
        }

        .menu-level.slide-in-from-right {
            transform: translateX(0);
        }

        .menu-level.slide-in-from-left {
            transform: translateX(0);
        }

        .search-container {
            padding: 20px;
            background-color: #f8f9fa;
        }

        .search-input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: none;
            border-radius: 25px;
            background-color: #e9ecef;
            font-size: 16px;
            outline: none;
            position: relative;
        }

        .search-container {
            position: relative;
        }

        .search-icon {
            position: absolute;
            right: 35px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 18px;
            cursor: pointer;
        }

        .menu-content {
            flex: 1;
            overflow-y: auto;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: background-color 0.2s ease;
            text-decoration: none;
            color: #333;
        }

        .menu-item:hover {
            background-color: #f8f9fa;
        }

        .menu-item .icon {
            margin-right: 15px;
            font-size: 18px;
            color: #6c757d;
            width: 20px;
        }

        .menu-item .text {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
        }

        .menu-item .arrow {
            color: #6c757d;
            font-size: 14px;
        }

        .menu-header-level {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background-color: #fff;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: #6c757d;
            cursor: pointer;
            margin-right: 15px;
            padding: 5px;
        }

        .menu-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            display: flex;
            align-items: center;
        }

        .menu-title .icon {
            margin-right: 10px;
            font-size: 18px;
        }

        .menu-footer {
            background-color: #1e3a5f;
            padding: 20px;
            margin-top: auto;
        }

        .footer-link {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .footer-link:hover {
            opacity: 0.9;
        }

        .submenu-item {
            display: flex;
            align-items: center;
            padding: 18px 20px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: background-color 0.2s ease;
            text-decoration: none;
            color: #333;
        }

        .submenu-item:hover {
            background-color: #f8f9fa;
        }

        .submenu-item .text {
            flex: 1;
            font-size: 16px;
        }

        .submenu-item .arrow {
            color: #6c757d;
            font-size: 14px;
        }

        /* Accessibility */
        .menu-item:focus,
        .submenu-item:focus,
        .back-btn:focus,
        .close-btn:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
        }

        /* Disable transitions during setup */
        .menu-level.no-transition {
            transition: none !important;
        }

        /* Ensure smooth animations */
        .menu-level.animating {
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="mobile-menu">
        <!-- Menu Header -->
        <div class="menu-header">
            <div class="time">9:41</div>
            <div class="status-icons">
                <i class="fas fa-user-circle" style="font-size: 20px;"></i>
                <i class="fas fa-globe" style="font-size: 18px;"></i>
            </div>
            <button class="close-btn" aria-label="Close menu">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="menu-container">
            <!-- Level 1 - Main Menu -->
            <div class="menu-level level-1 active" id="level1">
                <!-- Search -->
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search" aria-label="Search">
                    <i class="fas fa-search search-icon"></i>
                </div>

                <div class="menu-content">
                    <a href="#" class="menu-item" data-target="products" role="menuitem" aria-haspopup="true">
                        <i class="fas fa-laptop icon"></i>
                        <span class="text">Products</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="menu-item" data-target="services" role="menuitem" aria-haspopup="true">
                        <i class="fas fa-cogs icon"></i>
                        <span class="text">Services</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="menu-item" data-target="resources" role="menuitem" aria-haspopup="true">
                        <i class="fas fa-book icon"></i>
                        <span class="text">Resources</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="menu-item" data-target="partners" role="menuitem" aria-haspopup="true">
                        <i class="fas fa-handshake icon"></i>
                        <span class="text">Partners</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="menu-item" data-target="overview" role="menuitem" aria-haspopup="true">
                        <i class="fas fa-chart-bar icon"></i>
                        <span class="text">Overview</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="menu-item" data-target="events" role="menuitem" aria-haspopup="true">
                        <i class="fas fa-calendar icon"></i>
                        <span class="text">Events</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="menu-item" data-target="contact" role="menuitem">
                        <i class="fas fa-envelope icon"></i>
                        <span class="text">Contact Us</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 2 - Products Menu -->
            <div class="menu-level level-2" id="level2-products">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to main menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        <i class="fas fa-laptop icon"></i>
                        Products
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Ready Mix</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" data-target="concrete-products" role="menuitem" aria-haspopup="true">
                        <span class="text">Concrete Products</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Asphalt</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Aggregates</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 2 - Services Menu -->
            <div class="menu-level level-2" id="level2-services">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to main menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        <i class="fas fa-cogs icon"></i>
                        Services
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Consulting</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Technical Support</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Training</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Maintenance</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 2 - Resources Menu -->
            <div class="menu-level level-2" id="level2-resources">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to main menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        <i class="fas fa-book icon"></i>
                        Resources
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Documentation</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Case Studies</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">White Papers</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Downloads</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 2 - Partners Menu -->
            <div class="menu-level level-2" id="level2-partners">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to main menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        <i class="fas fa-handshake icon"></i>
                        Partners
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Technology Partners</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Channel Partners</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Integration Partners</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Become a Partner</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 2 - Overview Menu -->
            <div class="menu-level level-2" id="level2-overview">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to main menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        <i class="fas fa-chart-bar icon"></i>
                        Overview
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Company Overview</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Our Mission</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Leadership Team</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Careers</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 2 - Events Menu -->
            <div class="menu-level level-2" id="level2-events">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to main menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        <i class="fas fa-calendar icon"></i>
                        Events
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Upcoming Events</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Webinars</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Conferences</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Past Events</span>
                        <i class="fas fa-chevron-right arrow"></i>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>

            <!-- Level 3 - Concrete Products Menu -->
            <div class="menu-level level-3" id="level3-concrete-products">
                <div class="menu-header-level">
                    <button class="back-btn" aria-label="Back to products menu">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <div class="menu-title">
                        Concrete Products
                    </div>
                </div>

                <div class="menu-content">
                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">COMMANDbatch CP</span>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Marcotte Batch CP</span>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Precast Solutions</span>
                    </a>

                    <a href="#" class="submenu-item" role="menuitem">
                        <span class="text">Quality Control</span>
                    </a>
                </div>

                <div class="menu-footer">
                    <a href="#" class="footer-link">
                        Learn About Command Cloud
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            let currentLevel = 1;
            let levelHistory = [{level: 1, id: 'level1'}];
            let isAnimating = false;

            // Initialize menu - set level1 as active, others hidden
            function initializeMenu() {
                $('.menu-level').removeClass('active no-transition');
                $('.menu-level').addClass('no-transition');

                // Set initial positions without animation
                $('#level1').css('transform', 'translateX(0)').addClass('active');
                $('.menu-level:not(#level1)').css('transform', 'translateX(100%)');

                // Re-enable transitions after a brief delay
                setTimeout(() => {
                    $('.menu-level').removeClass('no-transition');
                }, 50);
            }

            // Menu navigation function with improved animation
            function navigateToLevel(targetLevel, targetId, direction = 'forward') {
                if (isAnimating) return; // Prevent multiple animations

                const currentLevelElement = $('.menu-level.active');
                const targetLevelElement = $('#' + targetId);

                if (!targetLevelElement.length || currentLevelElement.attr('id') === targetId) {
                    return; // Target doesn't exist or is already active
                }

                isAnimating = true;

                // Add animating class to prevent interactions
                $('.menu-level').addClass('animating');

                if (direction === 'forward') {
                    // Forward navigation: current slides left, new slides in from right

                    // Position target element off-screen to the right
                    targetLevelElement.addClass('no-transition');
                    targetLevelElement.css('transform', 'translateX(100%)');

                    // Re-enable transition and start animation
                    setTimeout(() => {
                        targetLevelElement.removeClass('no-transition');

                        // Animate current level out to the left
                        currentLevelElement.css('transform', 'translateX(-100%)');

                        // Animate target level in from the right
                        targetLevelElement.css('transform', 'translateX(0)');

                        // Update active states
                        currentLevelElement.removeClass('active');
                        targetLevelElement.addClass('active');
                    }, 10);

                    // Add to history
                    levelHistory.push({level: targetLevel, id: targetId});

                } else {
                    // Backward navigation: current slides right, previous slides in from left

                    // Position target element off-screen to the left
                    targetLevelElement.addClass('no-transition');
                    targetLevelElement.css('transform', 'translateX(-100%)');

                    // Re-enable transition and start animation
                    setTimeout(() => {
                        targetLevelElement.removeClass('no-transition');

                        // Animate current level out to the right
                        currentLevelElement.css('transform', 'translateX(100%)');

                        // Animate target level in from the left
                        targetLevelElement.css('transform', 'translateX(0)');

                        // Update active states
                        currentLevelElement.removeClass('active');
                        targetLevelElement.addClass('active');
                    }, 10);
                }

                currentLevel = targetLevel;

                // Clean up after animation completes
                setTimeout(() => {
                    isAnimating = false;
                    $('.menu-level').removeClass('animating');

                    // Reset positions of non-active levels
                    $('.menu-level:not(.active)').addClass('no-transition');
                    $('.menu-level:not(.active)').css('transform', 'translateX(100%)');

                    setTimeout(() => {
                        $('.menu-level').removeClass('no-transition');
                    }, 10);
                }, 320); // Slightly longer than transition duration
            }

            // Initialize menu on load
            initializeMenu();

            // Handle main menu item clicks
            $('.menu-item[data-target]').on('click', function(e) {
                e.preventDefault();
                const target = $(this).data('target');
                const targetId = 'level2-' + target;

                if ($('#' + targetId).length) {
                    navigateToLevel(2, targetId, 'forward');
                }
            });

            // Handle submenu item clicks for level 3
            $('.submenu-item[data-target]').on('click', function(e) {
                e.preventDefault();
                const target = $(this).data('target');
                const targetId = 'level3-' + target;

                if ($('#' + targetId).length) {
                    navigateToLevel(3, targetId, 'forward');
                }
            });

            // Handle back button clicks
            $('.back-btn').on('click', function(e) {
                e.preventDefault();

                if (isAnimating || levelHistory.length <= 1) return;

                // Remove current level from history
                levelHistory.pop();

                // Get previous level info
                const previousLevelInfo = levelHistory[levelHistory.length - 1];

                if (previousLevelInfo) {
                    navigateToLevel(previousLevelInfo.level, previousLevelInfo.id, 'backward');
                }
            });

            // Handle search functionality
            $('.search-input').on('input', function() {
                const searchTerm = $(this).val().toLowerCase();

                if (searchTerm.length > 0) {
                    // Simple search implementation
                    console.log('Searching for:', searchTerm);
                    // You can implement actual search logic here
                    // For example, filter menu items or show search results
                }
            });

            $('.search-icon').on('click', function() {
                const searchTerm = $('.search-input').val().toLowerCase();
                if (searchTerm.length > 0) {
                    console.log('Search triggered for:', searchTerm);
                    // Implement search action here
                }
            });

            // Handle search input enter key
            $('.search-input').on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    const searchTerm = $(this).val().toLowerCase();
                    if (searchTerm.length > 0) {
                        console.log('Search triggered for:', searchTerm);
                        // Implement search action here
                    }
                }
            });

            // Handle close button
            $('.close-btn').on('click', function() {
                // Reset to level 1
                $('.menu-level').removeClass('active slide-left slide-in-right slide-in-left');
                $('.menu-level').css('transform', 'translateX(100%)');
                $('#level1').addClass('active').css('transform', 'translateX(0)');
                currentLevel = 1;
                levelHistory = [1];

                // Clear search
                $('.search-input').val('');

                console.log('Menu closed');
                // You can add logic here to actually close/hide the menu
            });

            // Handle footer link clicks
            $('.footer-link').on('click', function(e) {
                e.preventDefault();
                console.log('Learn About Command Cloud clicked');
                // Implement footer link action here
            });

            // Keyboard navigation support
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape') {
                    $('.close-btn').click();
                }
            });

            // Touch event handling for better mobile experience
            let touchStartX = 0;
            let touchEndX = 0;

            $('.menu-container').on('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });

            $('.menu-container').on('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });

            function handleSwipe() {
                const swipeThreshold = 50;
                const swipeDistance = touchEndX - touchStartX;

                if (Math.abs(swipeDistance) > swipeThreshold) {
                    if (swipeDistance > 0 && currentLevel > 1) {
                        // Swipe right - go back
                        $('.back-btn').click();
                    }
                    // Swipe left functionality can be added here if needed
                }
            }

            // Accessibility improvements
            $('.menu-item, .submenu-item').on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    $(this).click();
                }
            });

            // Focus management
            $('.menu-level').on('transitionend', function() {
                if ($(this).hasClass('active')) {
                    // Focus first focusable element in the active level
                    $(this).find('.menu-item, .submenu-item, .back-btn').first().focus();
                }
            });
        });
    </script>
</body>
</html>