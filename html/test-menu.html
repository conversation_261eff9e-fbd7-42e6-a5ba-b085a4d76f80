<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mobile Menu</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        iframe {
            width: 100%;
            height: 852px;
            border: none;
        }
        .instructions {
            padding: 20px;
            background: #e8f4f8;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1e3a5f;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h3>🧪 Mobile Menu Refactoring Test</h3>
        <p><strong>Test the following functionality:</strong></p>
        <ul>
            <li>✅ <strong>Single Footer:</strong> Footer should remain visible on all menu levels</li>
            <li>✅ <strong>Navigation:</strong> Navigate between Level 1 → Level 2 → Level 3</li>
            <li>✅ <strong>Footer Clickable:</strong> "Learn About Command Cloud" should be clickable on all levels</li>
            <li>✅ <strong>No Overlap:</strong> Menu content should not overlap with footer</li>
            <li>✅ <strong>Smooth Animation:</strong> Transitions should be smooth without footer flickering</li>
            <li>✅ <strong>Back Navigation:</strong> Footer should remain stable during back navigation</li>
        </ul>
        <p><strong>Expected Result:</strong> One shared footer, no code duplication, consistent UX across all levels.</p>
    </div>

    <div class="test-container">
        <iframe src="menu.html"></iframe>
    </div>
</body>
</html>
